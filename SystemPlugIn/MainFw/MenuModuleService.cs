using JieNor.Framework.DataEntity.MainFw;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.AppService.SystemPlugIn.Auth;
using JieNor.Framework.Interface.BizTask;

namespace JieNor.Framework.AppService.SystemPlugIn.MainFw
{
    /// <summary>
    /// 首页菜单模块服务
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("listmenu")]
    public class MenuModuleService : AbstractOperationServicePlugIn
    {
        private static HtmlForm bizModuleMeta, menuGroupMeta, menuItemMeta;

        private static readonly string _AllMenuCacheKeyEx = "sysmenu:{0}";
        public static readonly string __AllMenuCacheKey = "sysmenu";

         

        protected override void InitializeServicePlugIn(OperationContext operCtx)
        {
            base.InitializeServicePlugIn(operCtx);

            UpdateMdlMeta(operCtx);
        }

        private void UpdateMdlMeta(OperationContext operCtx)
        {
            bizModuleMeta = HtmlParser.LoadFormMetaFromCache("sys_bizmodule", null);
            menuGroupMeta = HtmlParser.LoadFormMetaFromCache("sys_menugroup", null);
            menuItemMeta = HtmlParser.LoadFormMetaFromCache("sys_menuitem", null);

            var dm = this.GetDataManager();
            dm.Option.SetCacheData(false);
            dm.InitDbContext(operCtx.UserContext, bizModuleMeta.GetDynamicObjectType(operCtx.UserContext ));
            dm.InitDbContext(operCtx.UserContext, menuGroupMeta.GetDynamicObjectType(operCtx.UserContext));
            dm.InitDbContext(operCtx.UserContext, menuItemMeta.GetDynamicObjectType(operCtx.UserContext));
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var cacheKey = _AllMenuCacheKeyEx.Fmt($"{this.Context.UserId}");

            List<BizModuleItem> lstAllModules = null;
            //不从redis缓存取数，直接从数据库取数，因为redis缓存取数会报错导致权限丢失
            //var cache = PermHelper.GetRedisCacheSvc(this.Context);
            //try
            //{
            //    lstAllModules = cache.Get<List<BizModuleItem>>(this.Context, cacheKey);
            //}
            //catch (Exception ex)
            //{
            //    //忽略redis缓存取数错误
            //}

            if (lstAllModules == null || lstAllModules.Count == 0)
            {
                var strMenuItemSql = @"
                                    select t0.fmenuid
                                            ,t0.fname fmenuname
                                            ,t0.ficon fmenuicon
                                            ,t0.fhelpcode
                                            ,t0.forder fmenuorder
                                            ,t0.fdomaintype
                                            ,t0.fbillformid
                                            ,t0.fbillcaption
                                            ,t0.fenablerac
                                            ,t0.fparameter
                                            ,t0.fpermititemid
                                            ,t0.fhidden
                                            ,t0.fisadminfunc
                                            ,t1.fgroupid
                                            ,t1.fname fgroupname
                                            ,t1.forder fgrouporder
                                            ,t1.ficon fgroupicon
                                            ,t2.fmoduleid 
                                            ,t2.fname fmodulename
                                            ,t2.forder fmoduleorder
                                            ,t2.ficon fmoduleicon
                                    from t_sys_menuitem t0 with(nolock)
                                    inner join t_sys_menugroup t1  with(nolock) on t0.fgroupid=t1.fgroupid
                                    inner join t_sys_bizmodule t2  with(nolock) on t1.fmoduleid=t2.fmoduleid
                                    where t0.fhidden<>'1'
                                    ";
                if (this.Context.IsOperationCompany)
                {
                    strMenuItemSql += @" and t0.fisadminfunc='1' ";
                }

                if (this.Context.IsTopOrg)
                {
                    strMenuItemSql += " and t0.fistoporg='1' ";
                }
                else if (this.Context.IsSecondOrg)
                {
                    strMenuItemSql += " and t0.fissecondagent='1' ";
                }
                else
                {
                    strMenuItemSql += " and t0.fisagent='1' ";
                }

                if (!this.Context.IsTopOrg || !(this.Context.IsTopOrg && this.IsAdminRole()))
                {
                    strMenuItemSql += @"
                                        and (t0.fenablerac='0'
                                            or t0.fenablerac='1'
                                                and exists (
                                                    select top 1 1 
                                                    from T_SEC_ROLEFUNCACL u0 with(nolock)
                                                    inner join T_SEC_ROLEUSER u1 with(nolock) on u0.froleid=u1.froleid and u1.froleid!=''
                                                    where u0.fbizobjid=t0.fbillformid
                                                            and u0.fpermititemid=t0.fpermititemid
                                                            and u0.fallow='1'
                                                            and u1.fuserid=@fuserid
                                                )
                                    )
                                    ";
                }

                if (!PermHelper.IsAdministrator(this.Context))
                {
                    strMenuItemSql += @" and t0.fbillformid<>'sys_menueditor' ";
                }

                strMenuItemSql += @"
                                    order by t2.forder,t2.fmoduleid,t1.forder,t1.fgroupid,t0.forder,t0.fmenuid
                                    ";
                var menuItemObjs = this.DBService.ExecuteDynamicObject(this.Context, strMenuItemSql,
                    new SqlParam[]
                    {
                        new SqlParam("fuserid", System.Data.DbType.String,this.Context.UserId),
                    });

                lstAllModules = new List<BizModuleItem>();

                Dictionary<string, BizModuleItem> dctMenuModules = new Dictionary<string, BizModuleItem>(StringComparer.OrdinalIgnoreCase);
                Dictionary<string, MenuGroupItem> dctMenuGroups = new Dictionary<string, MenuGroupItem>(StringComparer.OrdinalIgnoreCase);

                foreach (var menuObj in menuItemObjs)
                {
                    BizModuleItem bizModuleItem = null;
                    var moduleId = menuObj["fmoduleid"] as string;
                    var groupId = menuObj["fgroupid"] as string;
                    var menuId = menuObj["fmenuid"] as string;

                    if (moduleId.IsNullOrEmptyOrWhiteSpace()
                        || groupId.IsNullOrEmptyOrWhiteSpace()
                        || menuObj.IsNullOrEmptyOrWhiteSpace()) continue;

                    if (!dctMenuModules.TryGetValue(moduleId, out bizModuleItem))
                    {
                        bizModuleItem = new BizModuleItem()
                        {
                            Id = moduleId,
                            Name = menuObj["fmodulename"] as string,
                            Icon = menuObj["fmoduleicon"] as string,
                            Order = Convert.ToInt32(menuObj["fmoduleorder"]),
                        };
                        bizModuleItem.MenuGroups = new List<MenuGroupItem>();
                        dctMenuModules[moduleId] = bizModuleItem;
                        lstAllModules.Add(bizModuleItem);
                    }
                    var menuGroupItem = bizModuleItem.MenuGroups.FirstOrDefault(o => o.Id.EqualsIgnoreCase(groupId));
                    if (menuGroupItem.IsNullOrEmpty())
                    {
                        menuGroupItem = new MenuGroupItem()
                        {
                            Id = groupId,
                            Name = menuObj["fgroupname"] as string,
                            Icon = menuObj["fgroupicon"] as string,
                            Order = Convert.ToInt32(menuObj["fgrouporder"]),
                        };
                        menuGroupItem.MenuItems = new List<MenuEntryItem>();
                        bizModuleItem.MenuGroups.Add(menuGroupItem);
                    }

                    MenuEntryItem menuEntryItem = new MenuEntryItem()
                    {
                        Id = menuId,
                        Name = menuObj["fmenuname"] as string,
                        Icon = menuObj["fmenuicon"] as string,
                        HelpCode = menuObj["fhelpcode"] as string,
                        BillFormId = menuObj["fbillformid"] as string,
                        BillCaption = menuObj["fbillcaption"] as string,
                        EnableRAC = (menuObj["fenablerac"] as string).EqualsIgnoreCase("1"),
                        Hidden = (menuObj["fhidden"] as string).EqualsIgnoreCase("1"),
                        PermitItemId = menuObj["fpermititemid"] as string,
                        Parameter = (menuObj["fparameter"] as string) ?? "",
                        DomainType = menuObj["fdomaintype"] as string,
                        Order = Convert.ToInt32(menuObj["fmenuorder"]),
                        IsAdminFunc = (menuObj["fisadminfunc"] as string).EqualsIgnoreCase("1"),
                    };

                    menuGroupItem.MenuItems.Add(menuEntryItem);
                }

                //cache.Set(this.Context, cacheKey, lstAllModules);
            }

            //处理当前用户会话权限
            var validBizModule = this.FilterBizModule(lstAllModules);

            this.Result.IsSuccess = true;
            this.Result.SrvData = validBizModule;
        }

        /// <summary>
        /// 管理角色
        /// </summary>
        /// <returns></returns>
        protected bool IsAdminRole()
        {
            var permData = new MetaCore.PermData.PermAuth(this.Context);
            return IsRoleAdmin(this.Context, permData) || this.Context.IsAdminRole(this.Context.Company) || this.Context.UserName.EqualsIgnoreCase("Administrator");
        }

        /// <summary>
        /// 判断用户是否属于管理员角色组
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        private bool IsRoleAdmin(UserContext ctx, PermAuth permData)
        {
            if (ctx.IsAdminRole(permData.CompanyId)) return true;

            var isAdmin = PermHelper.IsRoleAdmin(ctx, permData);

            return isAdmin;
        }

        /// <summary>
        /// 主控台模块及菜单过滤
        /// </summary>
        /// <param name="bizModules"></param>
        /// <returns></returns>
        protected virtual IEnumerable<BizModuleItem> FilterBizModule(List<BizModuleItem> bizModules)
        {
            List<BizModuleItem> lst = new List<BizModuleItem>(bizModules);

            //各业务系统依据需要再自己过滤一遍
            var filter = this.Container.GetService< IEnumerable<IBizModuleView>>();
            if (filter != null)
            {
                List<BizModuleItem> listValidModule = lst;
                foreach (var item in filter)
                {
                     listValidModule = item.FilterBizModule(this.Context, listValidModule).ToList ();
                }
                
                return listValidModule;
            }

            return lst;
        }

        /// <summary>
        /// 检查是否需要进行菜单的权限验证
        /// </summary>
        /// <param name="bizModules"></param>
        /// <returns></returns>
        private static bool JudeCheckPermisson(List<BizModuleItem> bizModules)
        {
            foreach (var mdl in bizModules)
            {
                foreach (var item in mdl.MenuGroups)
                {
                    if (item.MenuItems.Any(f => f.PermitItemId.IsNullOrEmptyOrWhiteSpace() == false))
                    {
                        return true;
                    }
                }
            }

            return false;
        }
    }

    /// <summary>
    /// 读取所有菜单，只允许菜单编辑器调用
    /// </summary>
    [InjectService]
    [FormId("sys_menueditor")]
    [OperationNo("listmenu")]
    public class MenuModuleServiceEx : AbstractOperationServicePlugIn
    {
        private static HtmlForm bizModuleMeta, menuGroupMeta, menuItemMeta;

        static MenuModuleServiceEx()
        {
            bizModuleMeta = HtmlParser.LoadFormMetaFromCache("sys_bizmodule", null);
            menuGroupMeta = HtmlParser.LoadFormMetaFromCache("sys_menugroup", null);
            menuItemMeta = HtmlParser.LoadFormMetaFromCache("sys_menuitem", null);
        }

        protected override void InitializeServicePlugIn(OperationContext operCtx)
        {
            base.InitializeServicePlugIn(operCtx);

            UpdateMdlMeta(operCtx);
        }

        private void UpdateMdlMeta(OperationContext operCtx)
        {
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        { 
            var lstAllModules = new List<BizModuleItem>();
             
            var pkIdReader = this.Context.GetPkIdDataReader(bizModuleMeta, "fmoduleid<>''", new SqlParam[] { });
            var bizModuleDM = this.Context.GetDefaultDataManager(bizModuleMeta);
            var allModuleObjs = bizModuleDM.SelectBy(pkIdReader, this.Option);
             
            pkIdReader = this.Context.GetPkIdDataReader(menuGroupMeta, "fgroupid<>''", new SqlParam[] { });
            var menuGroupDM = this.Context.GetDefaultDataManager(menuGroupMeta);
            var allMenuGroupObjs = menuGroupDM.SelectBy(pkIdReader, this.Option);
             
            pkIdReader = this.Context.GetPkIdDataReader(menuItemMeta, "fmenuid<>''", new SqlParam[] { });
            var menuItemDM = this.Context.GetDefaultDataManager(menuItemMeta);
            var allMenuItemObjs = menuItemDM.SelectBy(pkIdReader, this.Option);

            foreach (DynamicObject module in allModuleObjs)
            {
                var moduleId = Convert.ToString(module["id"]);
                BizModuleItem bizModuleItem = module;
                bizModuleItem.MenuGroups = new List<MenuGroupItem>();
                foreach (DynamicObject menugroup in allMenuGroupObjs
                    .OfType<DynamicObject>()
                    .Where(o => Convert.ToString(o["fmoduleid"]).EqualsIgnoreCase(moduleId)))
                {
                    var menuGroupId = Convert.ToString(menugroup["Id"]);
                    MenuGroupItem menuGroupItem = menugroup;
                    menuGroupItem.MenuItems = new List<MenuEntryItem>();
                    foreach (DynamicObject menuItem in allMenuItemObjs
                        .OfType<DynamicObject>()
                        .Where(o => Convert.ToString(o["fgroupid"]).EqualsIgnoreCase(menuGroupId))
                        .OrderBy(o => o["forder"]))
                    {
                        menuGroupItem.MenuItems.Add(menuItem);
                    }

                    bizModuleItem.MenuGroups.Add(menuGroupItem);
                }

                lstAllModules.Add(bizModuleItem);
            }

            //处理当前用户会话权限
            this.Result.IsSuccess = true;
            this.Result.SrvData = lstAllModules;
        }

    }
}
