using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.MP.API.Response.YDJ.STE.Order.PushOrder;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.MerChant;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：焕新订单-提交总部
    /// 直接用销售合同提交至中台
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("SubmitHeadquart")]
    public class SubmitHeadQuart : AbstractOperationServicePlugIn
    {
        private int ftoppiecesendtag { get; set; }
        private int fmanagemodel { get; set; }

        /// <summary>
        /// 转采购失败的门店上样销售合同
        /// </summary>
        private List<DynamicObject> ErrorStoreSampleDys { set; get; }

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype", "fproductid" });
        }
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            //这哥插件是普通和焕新订单都可以使用的，校验器里去加校验规则
            base.PrepareValidationRules(e);

            var isList = false;

            isList = this.OperationContext is ListOperationContext ? true : false;

            e.Rules.Add(new Validation_SubmitHeadQuart(isList));
        }

        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            var renewalflagFld = this.HtmlForm.GetField("frenewalflag");
            if (renewalflagFld == null)
            {
                throw new BusinessException("销售合同模型没有【焕新订单标记】字段，请检查");
            }
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            base.BeginOperationTransaction(e);
            var agent = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "ftoppiecesendtag,fmanagemodel");
            this.ftoppiecesendtag = Convert.ToInt32(agent["ftoppiecesendtag"]);
            this.fmanagemodel = Convert.ToInt32(agent["fmanagemodel"]);
            var reNewLOrderDys = e.DataEntitys.Where(x => Convert.ToBoolean(Convert.ToInt32(x["frenewalflag"]))).ToList();

            var normalOrderDys = e.DataEntitys.Where(x => !Convert.ToBoolean(Convert.ToInt32(x["frenewalflag"]))).ToList();

            if (reNewLOrderDys != null && reNewLOrderDys.Any())
            {
                BeginOperationTransactionReNewOrder(reNewLOrderDys);
            }

            if (normalOrderDys != null && normalOrderDys.Any())
            {
                BeginOperationTransactionNormalOrder(normalOrderDys);
            }

            if (ErrorStoreSampleDys != null && ErrorStoreSampleDys.Any())
            {
                var filterDataEntitys = e.DataEntitys.Where(x => !ErrorStoreSampleDys.Any(y => Convert.ToString(y["id"]).Equals(x["id"]))).ToArray();
                //把有错误的数据包给过滤掉
                e.DataEntitys = filterDataEntitys;
            }

            this.AddRefreshPageAction();
        }

        /// <summary>
        /// 处理焕新订单的逻辑
        /// </summary>
        /// <param name="reNewOrderDys"></param>
        private void BeginOperationTransactionReNewOrder(IEnumerable<DynamicObject> reNewOrderDys)
        {
            if (reNewOrderDys == null || !reNewOrderDys.Any()) return;

            var orderService = this.Container.GetService<IOrderService>();

            // 基于合同生成收款单，系统自动提交审核。（字段映射逻辑请看下方单据转换：销售合同->收款单）
            var incomeDisburseMeta = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");

            foreach (var order in reNewOrderDys)
            {
                var fentrylist = order["fentry"] as DynamicObjectCollection;
                order["fsettlprogress"] = Enu_RenewalSettleProgress.已收款; //已收款
                //自动更新【提交总部时间】【协同总部状态=提交至总部】
                order["fsubmithtime"] = DateTime.Now;
                if (Convert.ToString(order["fchstatus"]).EqualsIgnoreCase("2") || Convert.ToString(order["fchstatus"]).EqualsIgnoreCase("4"))
                {
                    order["fheadquartsyncmessage"] = string.Empty;
                }
                order["fchstatus"] = "1";
                // 获取单据类型名称
                var billTypeName = Convert.ToString((order["fbilltype_ref"] as DynamicObject)?["fname"]);
                var renewalFlag = Convert.ToBoolean(order["frenewalflag"]);

                //
                // 1. 计算四舍五入到两位小数的成交单价 不管是一级销售合同还是二级分销提交上来的二级销售合同
                foreach (var fentry in fentrylist)
                {
                    //根据每一行商品品{ 四舍五入二位.【零售价】-四舍五入二位.（【零售价】－【成交单价】）}价】作为【保留2位小数成交单价】
                    decimal cvalue = Convert.ToDecimal(fentry["fprice"].ToString()) - Convert.ToDecimal(fentry["fdealprice"].ToString());
                    fentry["rounded_fdealprice"] = decimal.Round(Convert.ToDecimal(fentry["fprice"].ToString()), 2, MidpointRounding.AwayFromZero) - decimal.Round(cvalue, 2, MidpointRounding.AwayFromZero);
                }
                // 2. 检查所有行是否都需要处理
                bool needProcess = !fentrylist.All(d => Convert.ToDecimal(d["rounded_fdealprice"]) == Convert.ToDecimal(d["fdealprice"]));
                if (needProcess)
                {
                    // 3. 计算行尾差值
                    foreach (var fentry in fentrylist)
                    {
                        var roundedOriginalAmount = decimal.Round(Convert.ToDecimal(fentry["fdealamount"]), 2, MidpointRounding.AwayFromZero); //四舍五入成交金额
                        var roundedFDealAmount = Convert.ToDecimal(fentry["fbizqty"]) * Convert.ToDecimal(fentry["rounded_fdealprice"]); //【保留2位小数成交单价】乘以当前行【销售数量】，
                        fentry["rounded_fdealamount"] = roundedOriginalAmount - roundedFDealAmount; //计算得出行尾差值
                    }
                    // 4. 整单检查及处理
                    ProcessWholeOrderDifference(order, "rounded_fdealamount");
                }
                //SetPiecesTag(order, fentrylist);

                var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new List<DynamicObject> { order }, "draft",
                    new Dictionary<string, object> { { "IgnoreCheckPermssion", "true" } });
                result.ThrowIfHasError();

                var payOrder = incomeDisburseMeta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
                payOrder["frenewalflag"] = true;
                payOrder["fdate"] = DateTime.Now;
                payOrder["fway"] = "payway_14"; //焕新合同收款
                payOrder["fpurpose"] = "bizpurpose_02";  //订单付款
                payOrder["fdirection"] = "direction_02";    //减
                payOrder["fbizdirection"] = "bizdirection_01";  //收入
                payOrder["fsourceformid"] = "ydj_order";
                payOrder["fsourcenumber"] = order["fbillno"];
                payOrder["fsourceid"] = order["id"];
                payOrder["fstaffid"] = order["fstaffid"];
                payOrder["fdeptid"] = order["fdeptid"];
                payOrder["fcustomerid"] = order["fcustomerid"];
                payOrder["fmembershiptranid"] = order["fmembershiptranid"];
                payOrder["famount"] = order["fdealamount"];
                payOrder["paymentdesc"] = "790610086597890051";   //合同款

                //是否焕新订单合同接收通知操作
                var MSRenewalNotify = this.GetQueryOrSimpleParam<bool>("MSRenewalNotify", false);

                if (!MSRenewalNotify)
                {
                    result = this.Gateway.InvokeBillOperation(this.Context, incomeDisburseMeta.Id, new[] { payOrder }, "save",
                        new Dictionary<string, object> { { "IgnoreValidateDataEntities", true }, { "NotAutoSubmit", true }, { "IgnoreCheckPermssion", "true" } });
                    result.ThrowIfHasError();

                    result = this.Gateway.InvokeBillOperation(this.Context, incomeDisburseMeta.Id, new[] { payOrder }, "submit", new Dictionary<string, object> { { "IgnoreValidateDataEntities", true }, { "IgnoreCheckPermssion", "true" } });
                    result.ThrowIfHasError();

                    result = this.Gateway.InvokeBillOperation(this.Context, incomeDisburseMeta.Id, new[] { payOrder }, "audit", new Dictionary<string, object> { { "IgnoreValidateDataEntities", true }, { "IgnoreCheckPermssion", "true" } });
                    result.ThrowIfHasError();
                }
            }
            
            /*
             * 一级经销商：生成采购订单并自动调用提交审核及<焕新订单提交总部>。
             * 二级经销商：生成采购订单并自动调用提交审核及<提交一级经销>。
             */

            List<string> purchaseOrderIds = new List<string>();

            var v6Orders = reNewOrderDys.Where(s =>
                Convert.ToString((s["fbilltype_ref"] as DynamicObject)?["fname"]).EqualsIgnoreCase("v6定制柜合同"));

            if (v6Orders.Any())
            {
                var selectRowIds = v6Orders.SelectMany(s => s["fentry"] as DynamicObjectCollection)
                    .Select(s => new Row { Id = Convert.ToString(s["id"]) });
                orderService.CalculateEntrySubsidyamount(this.Context, v6Orders);
                //处理国补预收字段
                foreach (var v6OrderItem in v6Orders)
                {
                    var objDetail = v6OrderItem["fentry"] as DynamicObjectCollection;
                    var firstEnd = objDetail.Where(a => Convert.ToString(a["fomsprogress"]).EqualsIgnoreCase(Enu_OMSProgress.流程完成)).FirstOrDefault();
                    if (firstEnd != null)
                    {
                        firstEnd["fsubrecdealamount_gb"] = v6OrderItem["frecdealamount_gb"];
                        firstEnd["fsubrecsumamount_gb"] = v6OrderItem["frecsumamount_gb"];
                    }
                    int seq = 1;
                    //处理国补预收字段
                    foreach (var item in objDetail)
                    {
                        var fomsprogress = Convert.ToString(item["fomsprogress"]);
                        if (!fomsprogress.EqualsIgnoreCase(Enu_OMSProgress.单据作废))
                        {
                            if (!Convert.ToString(item["id"]).Equals(Convert.ToString(firstEnd?["id"])))
                            {
                                item["fsubrecdealamount_gb"] = "0.01";
                                item["fsubrecsumamount_gb"] = "0.01";
                            }
                            item["fsubrenewseq"] = seq;
                            seq++;
                        }
                    }
                }

                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                dm.Save(v6Orders);
            }
            SetDirectOrderProductEntryCommissionAmount(this.Context, reNewOrderDys.ToArray());

            this.Result.IsSuccess = true;

        }

        /// <summary>
        /// 处理正常订单的逻辑
        /// </summary>
        /// <param name="normalOrderDys"></param>
        private void BeginOperationTransactionNormalOrder(IEnumerable<DynamicObject> normalOrderDys)
        {
            if (normalOrderDys == null || !normalOrderDys.Any()) return;

            foreach (var normalOrderDy in normalOrderDys)
            {
                var fentrylist = normalOrderDy["fentry"] as DynamicObjectCollection;

                //自动更新【提交总部时间】【协同总部状态=提交至总部】
                normalOrderDy["fsubmithtime"] = DateTime.Now;
                if (Convert.ToString(normalOrderDy["fchstatus"]).EqualsIgnoreCase("2") || Convert.ToString(normalOrderDy["fchstatus"]).EqualsIgnoreCase("4"))
                {
                    normalOrderDy["fheadquartsyncmessage"] = string.Empty;
                }
                normalOrderDy["fchstatus"] = "1";

                // 1. 计算四舍五入到两位小数的成交单价,不管是一级销售合同还是二级分销提交上来的二级销售合同
                foreach (var fentry in fentrylist)
                {
                    //根据每一行商品品{ 四舍五入二位.【零售价】-四舍五入二位.（【零售价】－【成交单价】）}价】作为【保留2位小数成交单价】
                    decimal cvalue = Convert.ToDecimal(fentry["fprice"].ToString()) - Convert.ToDecimal(fentry["fdealprice"].ToString());
                    fentry["rounded_fdealprice"] = decimal.Round(Convert.ToDecimal(fentry["fprice"].ToString()), 2, MidpointRounding.AwayFromZero) - decimal.Round(cvalue, 2, MidpointRounding.AwayFromZero);
                }
                // 2. 检查所有行是否都需要处理
                bool needProcess = !fentrylist.All(d => Convert.ToDecimal(d["rounded_fdealprice"]) == Convert.ToDecimal(d["fdealprice"]));
                if (needProcess)
                {
                    // 3. 计算行尾差值
                    foreach (var fentry in fentrylist)
                    {
                        var roundedOriginalAmount = decimal.Round(Convert.ToDecimal(fentry["fdealamount"]), 2, MidpointRounding.AwayFromZero); //四舍五入成交金额
                        var roundedFDealAmount = Convert.ToDecimal(fentry["fbizqty"]) * Convert.ToDecimal(fentry["rounded_fdealprice"]); //【保留2位小数成交单价】乘以当前行【销售数量】，
                        fentry["frounded_fdirectdealamount"] = roundedOriginalAmount - roundedFDealAmount; //计算得出行尾差值
                    }
                    // 4. 整单检查及处理,处理尾插
                    ProcessWholeOrderDifference(normalOrderDy, "frounded_fdirectdealamount");
                }
                //SetPiecesTag(normalOrderDy, fentrylist);
            }
            SetDirectOrderProductEntryCommissionAmount(this.Context, normalOrderDys.ToArray());
            var getStoreSampleDys = GetDirectSaleOrderBillTypeIsStoreSamplesDys(normalOrderDys.ToArray());
            DirectSaleOrderBillTypeIsStoreSamples(getStoreSampleDys.ToArray());
        }

        /// <summary>
        /// 整单检查及处理
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="filedIdStr">尾差的属性名</param>
        //赋值尾差
        private void ProcessWholeOrderDifference(DynamicObject dataEntity, string filedIdStr)
        {
            string billTypeName = (dataEntity["fbilltype_ref"] as DynamicObject)?["fname"].ToString();
            var fentrylist = dataEntity["fentry"] as DynamicObjectCollection;

            //直营销售合同，赠品也是要计算的，赠品也可以有值
            var validDetails = fentrylist.Where(d => !Convert.ToBoolean((d["fproductid_ref"] as DynamicObject)?["fsuiteflag"]))
                                                            .ToList();
            // var validDetails = fentrylist.Where(d => !Convert.ToBoolean((d["fproductid_ref"] as DynamicObject)?["fsuiteflag"]) && !Convert.ToBoolean(d["fisgiveaway"]))
            //  .ToList();
            if (billTypeName.EqualsIgnoreCase("v6定制柜合同"))
            {
                validDetails = validDetails
                .Where(d => Convert.ToString(d["fomsprogress"]) != "-1")
                .ToList();
            }
            if (!validDetails.Any()) return;
            // 计算总差异
            decimal totalNewAmount = validDetails.Sum(d => Convert.ToDecimal(d["fbizqty"]) * Convert.ToDecimal(d["rounded_fdealprice"]) + Convert.ToDecimal(d[filedIdStr]));
            decimal difference = totalNewAmount - Convert.ToDecimal(dataEntity["fdealamount"]);
            if (difference == 0M) return;
            // 找出ID最大的有效明细行
            var lastDetail = validDetails.OrderByDescending(d => Convert.ToString(d["Id"])).First();
            // 更新尾差
            lastDetail[filedIdStr] = Convert.ToDecimal(lastDetail[filedIdStr]) + difference;
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //焕新订单
            var reNewLOrderDys = e.DataEntitys.Where(x => Convert.ToBoolean(Convert.ToInt32(x["frenewalflag"]))).ToList();

            //正常订单
            var normalOrderDys = e.DataEntitys.Where(x => !Convert.ToBoolean(Convert.ToInt32(x["frenewalflag"]))).ToList();

            if (normalOrderDys != null && normalOrderDys.Any())
            {
                AfterExecuteOperationTransactionNormalOrder(normalOrderDys);
            }

            if (reNewLOrderDys != null && reNewLOrderDys.Any())
            {
                AfterExecuteOperationTransactionReNewOrder(reNewLOrderDys);
            }
        }

        /// <summary>
        /// 处理正常销售合同的逻辑
        /// </summary>
        /// <param name="normalOrderDys">销售合同数据包</param>
        public void AfterExecuteOperationTransactionNormalOrder(IEnumerable<DynamicObject> normalOrderDys)
        {
            if (this.Context.IsSecondOrg)
            {
            }
            else
            {
                var storeSampleOrderDys = GetDirectSaleOrderBillTypeIsStoreSamplesDys(normalOrderDys.ToArray());
                var notStoreSampleOrderDys = new List<DynamicObject>();
                if (storeSampleOrderDys != null && storeSampleOrderDys.Any())
                {
                    notStoreSampleOrderDys = normalOrderDys.Where(x => !storeSampleOrderDys.Any(y => Convert.ToString(y["id"]).Equals(x["id"]))).ToList();
                }
                //如果没有门店上样的话，那就用自己的
                else
                {
                    notStoreSampleOrderDys = normalOrderDys.ToList();
                }

                //不是门店上样的才在销售合同上提交总部
                if (notStoreSampleOrderDys != null && notStoreSampleOrderDys.Any())
                {
                    var orderFormId = "ydj_order";
                    try
                    {
                        // 焕新订单提交总部
                        this.Gateway.InvokeBillOperation(this.Context, orderFormId, normalOrderDys, "normalordersubmithq",
                            new Dictionary<string, object>
                            {
                                { "IgnoreCheckPermssion", true }, { "IgnoreValidateDataEntities", true },{ "__IgnoreOpLog__", true }  // 正确的忽略操作日志参数
                            });
                    }
                    catch (Exception ex)
                    {
                        throw new BusinessException(ex.Message);
                    }
                    finally
                    {
                    }
                }
            }
        }

        /// <summary>
        /// 处理焕新订单的逻辑
        /// </summary>
        /// <param name="reNewOrderDys"></param>
        public void AfterExecuteOperationTransactionReNewOrder(IEnumerable<DynamicObject> reNewOrderDys)
        {
            if (this.Context.IsSecondOrg )
            {
            }
            else
            {
                var orderFormId = "ydj_order";
                try
                {
                    // 焕新订单提交总部
                    this.Gateway.InvokeBillOperation(this.Context, orderFormId, reNewOrderDys, "renewordersubmithq",
                    new Dictionary<string, object>
                    {
                            { "IgnoreCheckPermssion", true }, { "IgnoreValidateDataEntities", true }
                    });
                }
                catch (Exception ex)
                {
                }
                finally
                {
                }

            }
        }

        /// <summary>
        /// 赋值一件代发逻辑
        /// </summary>
        /// <param name="order"></param>
        /// <param name="fentrylist"></param>
        /// <exception cref="BusinessException"></exception>
        private void SetPiecesTag(DynamicObject order, DynamicObjectCollection fentrylist)
        {
            if (fentrylist == null || fentrylist.Count <= 0) return;
            bool hasPartialtype1 = fentrylist.Any(a => Convert.ToString(a["fdeliverytype"]) == "delivery_type_02");
            bool hasPartialtype2 = fentrylist.Any(a => Convert.ToString(a["fdeliverytype"]) == "delivery_type_01");
            if (hasPartialtype1 && hasPartialtype2)
            {
                throw new BusinessException("直营销售订单商品行需按交货方式分开录单，门店仓出现货与总部直发不可混录。");
            }
            // 判断所有商品明细行【出现货】都未勾选
            //bool allNotOutSpot = fentrylist.All(x => !Convert.ToBoolean(x["fisoutspot"]));

            //if (allNotOutSpot && fmanagemodel == 1 && ftoppiecesendtag == 1)
            //{
            //    foreach (var entry in fentrylist)
            //    {
            //        entry["fdeliverytype"] = "delivery_type_02"; // 门店直发
            //    }
            //}
            //else if (!allNotOutSpot && fmanagemodel == 1 && ftoppiecesendtag == 1)
            //{
            //    // 自动勾选一件代发
            //    order["fpiecesendtag"] = true;
            //    // 所有明细行交货方式赋值为总部直发
            //    foreach (var entry in fentrylist)
            //    {
            //        entry["fdeliverytype"] = "delivery_type_01"; // 总部直发
            //    }
            //}


            if (fmanagemodel == 1 && ftoppiecesendtag == 1)
            {
                var billTypeName = Convert.ToString((order["fbilltype_ref"] as DynamicObject)?["fname"]);
                // 判断所有商品明细行【出现货】都未勾选
                bool allNotOutSpot = fentrylist.All(x => !Convert.ToBoolean(x["fisoutspot"]));
                bool allOutSpot = fentrylist.All(x => Convert.ToBoolean(x["fisoutspot"]));
                if (billTypeName.Equals("门店上样")) return;

                bool frenewalflag = Convert.ToBoolean(order["frenewalflag"]);
                if (frenewalflag)
                {
                    if (frenewalflag && allNotOutSpot)
                    {
                        // 自动勾选一件代发
                        order["fpiecesendtag"] = true;
                        // 所有明细行交货方式赋值为总部直发
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = "delivery_type_01"; // 总部直发
                        }
                    }
                    else if (frenewalflag && allOutSpot)
                    {
                        // 自动勾选一件代发
                        order["fpiecesendtag"] = false;
                        // 所有明细行交货方式赋值为门店直发
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = "delivery_type_02"; // 门店直发
                        }
                    }
                    else
                    {
                        // 
                        order["fpiecesendtag"] = false;
                        // 所有明细行交货方式赋值为
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = ""; // 
                        }
                    }
                }
                else if (!billTypeName.Equals("门店上样"))
                {
                    if (allNotOutSpot)
                    {
                        // 自动勾选一件代发
                        order["fpiecesendtag"] = true;
                        // 所有明细行交货方式赋值为总部直发
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = "delivery_type_01"; // 总部直发
                        }
                    }
                    else if (allOutSpot)
                    {
                        // 自动勾选一件代发
                        order["fpiecesendtag"] = false;
                        // 所有明细行交货方式赋值为总部直发
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = "delivery_type_02"; // 总部直发
                        }
                    }
                    else
                    {
                        // 
                        order["fpiecesendtag"] = false;
                        // 所有明细行交货方式赋值为
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = ""; // 
                        }
                    }
                }
                if (allOutSpot == false && allNotOutSpot == false)
                {
                    throw new BusinessException("直营销售订单商品行需按交货方式分开录单，门店仓出现货与总部直发不可混录。");
                }

                var _entry = order["fentry"] as DynamicObjectCollection;
                if (_entry != null && Convert.ToBoolean(order["fpiecesendtag"]))
                {
                    foreach (var item in _entry)
                    {
                        var bizqty = Convert.ToDecimal(item["fbizqty"]);
                        var fseq = Convert.ToDecimal(item["fseq"]);
                        var fdeliverytype = Convert.ToString(item["fdeliverytype"]);
                        if (fdeliverytype.Equals("delivery_type_01"))
                        {
                            var matNumber = Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fnumber"]);
                            var packqty = Convert.ToDecimal((item["fproductid_ref"] as DynamicObject)?["fpackqty"]);
                            if (packqty > 1)
                            {
                                if (bizqty % packqty > 0)
                                {
                                    throw new BusinessException($@"销售合同{order["fbillno"]},第[{fseq}]行商品[{matNumber}]属于箱类商品，交货方式为“总部直发”，但是销售数量不满足整箱倍数，不允许一件代发操作，请核查！");
                                }
                            }
                        }
                    }
                }
            }

        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null && e.DataEntitys.Length <= 0) return;

            var reNewLOrderDys = e.DataEntitys.Where(x => Convert.ToBoolean(Convert.ToInt32(x["frenewalflag"]))).ToList();

            var normalOrderDys = e.DataEntitys.Where(x => !Convert.ToBoolean(Convert.ToInt32(x["frenewalflag"]))).ToList();

            if (normalOrderDys != null && normalOrderDys.Any())
            {
                EndOperationTransactionTransactionNormalOrder(normalOrderDys);
            }

        }

        /// <summary>
        /// 事务结束前，处理正常的销售合同(数据已提交到数据库中，但事务还没结束)
        /// </summary>
        /// <param name="normalOrderDys"></param>
        private void EndOperationTransactionTransactionNormalOrder(IEnumerable<DynamicObject> normalOrderDys)
        {
            if (normalOrderDys == null || !normalOrderDys.Any()) return;

            var storeSamplesOrderDys = GetDirectSaleOrderBillTypeIsStoreSamplesDys(normalOrderDys.ToArray());

            if (storeSamplesOrderDys != null && storeSamplesOrderDys.Any())
            {
                //StoreSampleOrderDyAssociatePurchaseOrderSubmitHq(storeSamplesOrderDys.ToArray());
            }
        }

        /// <summary>
        /// 获取当前数据包中单据类型为【门店上样】的数据包
        /// </summary>
        /// <param name="orderDys"></param>
        /// <returns></returns>
        public IEnumerable<DynamicObject> GetDirectSaleOrderBillTypeIsStoreSamplesDys(DynamicObject[] orderDys)
        {
            var storeSampleDys = new List<DynamicObject>();
            //销售合同
            foreach (var orderDy in orderDys)
            {
                var billTypeDy = orderDy["fbilltype_ref"] as DynamicObject;
                var billTypeName = Convert.ToString(billTypeDy["fname"]);
                //
                if (billTypeName.EqualsIgnoreCase("门店上样"))
                {
                    storeSampleDys.Add(orderDy);
                }
            }

            return storeSampleDys;
        }

        /// <summary>
        /// 处理直营销售订单单据类型为【门店上样】，需要转采购，然后再提交总部
        /// </summary>
        /// <param name="storeSamplesOrderDys"></param>
        private void DirectSaleOrderBillTypeIsStoreSamples(DynamicObject[] storeSamplesOrderDys)
        {
            if (storeSamplesOrderDys == null || storeSamplesOrderDys.Length <= 0) return;
            var orderNums = storeSamplesOrderDys.Select(x => Convert.ToString(x["fbillno"])).ToList();
            var sqlStr = $@" select typ.fid as 'billhead_id',
                                    typ.fbillno as 'purchaseorderno',
                                    typoe.fsourcebillno as 'sourceorderno'
                                   from t_ydj_purchaseorder typ with(nolock )
                                    inner join t_ydj_poorderentry typoe with(nolock ) on typ.fid = typoe.fid
                            where typ.fmainorgid = '{this.Context.Company}' 
                                    and typ.fcancelstatus = '0'
                                    and typoe.fsourceformid = 'ydj_order'
                                    and typoe.fsourcebillno in ({string.Join(",", orderNums.Select(x => $"'{x}'"))})
                        ";

            if (ErrorStoreSampleDys == null)
            {
                ErrorStoreSampleDys = new List<DynamicObject>();
            }

            var tempPurChaseOrderIdDys = DBService.ExecuteDynamicObject(this.Context, sqlStr);

            //下推成功的销售合同
            var sucessOrderDys = new List<DynamicObject>();

            if (tempPurChaseOrderIdDys == null || !tempPurChaseOrderIdDys.Any())
            {
                var option = new Dictionary<string, object>();

                /*foreach (var storeSamplesOrderDy in storeSamplesOrderDys)
                {

                }*/
                
                if (ErrorStoreSampleDys == null)
                {
                    ErrorStoreSampleDys = new List<DynamicObject>();
                }

                foreach (var storeSamplesOrderDy in storeSamplesOrderDys)
                {
                    //门店上样的要转采购
                    var operResult = Gateway.InvokeBillOperation(this.Context, "ydj_order", new[] { storeSamplesOrderDy }, "pushpurorder", option);

                    //检验不通过也返回一个true?(出现货不允许采购) 难崩
                    if (!operResult.IsSuccess || (operResult.ComplexMessage.ErrorMessages.Any() || operResult.ComplexMessage.WarningMessages.Any()))
                    {
                        var errorOrderNo = Convert.ToString(storeSamplesOrderDy["fbillno"]);
                        var joinErrorStr = string.Join(",", operResult.ComplexMessage.ErrorMessages.Select(x => x));
                        var errorMsg = $"单据类型为【门店上样】的销售合同【{errorOrderNo}】转采购失败，{joinErrorStr}";
                        this.Result.ComplexMessage.ErrorMessages.Add(errorMsg);
                        ErrorStoreSampleDys.Add(storeSamplesOrderDy);

                        continue;
                    }
                    else
                    {
                        sucessOrderDys.Add(storeSamplesOrderDy);
                    }
                }
            }
            else
            {
                //还没有下推采购订单的
               var notHasPurchaseOrderStoreSamplesOrderDys = storeSamplesOrderDys.Where(x => !tempPurChaseOrderIdDys.Any(y =>
                                                                                                            Convert.ToString(x["fbillno"]).Equals(Convert.ToString(y["sourceorderno"]))))
                                                                                                    .ToList();
               
               if (ErrorStoreSampleDys == null)
               {
                   ErrorStoreSampleDys = new List<DynamicObject>();
               }
               //
               if (notHasPurchaseOrderStoreSamplesOrderDys != null && notHasPurchaseOrderStoreSamplesOrderDys.Any())
               {
                   var option = new Dictionary<string, object>();

                   foreach (var notHasPurchaseOrderStoreSamplesOrderDy in notHasPurchaseOrderStoreSamplesOrderDys)
                   {
                       //门店上样的要转采购
                       var operResult = Gateway.InvokeBillOperation(this.Context, "ydj_order", new[] { notHasPurchaseOrderStoreSamplesOrderDy }, "pushpurorder", option);
                       //检验不通过也返回一个true?(出现货不允许采购) 难崩
                       if (!operResult.IsSuccess || (operResult.ComplexMessage.ErrorMessages.Any() || operResult.ComplexMessage.WarningMessages.Any()))
                       {
                           var errorOrderNo = Convert.ToString(notHasPurchaseOrderStoreSamplesOrderDy["fbillno"]);
                           var joinErrorStr = string.Join(",", operResult.ComplexMessage.ErrorMessages.Select(x => x));
                           var errorMsg = $"单据类型为【门店上样】的销售合同【{errorOrderNo}】转采购失败，{joinErrorStr}";
                           this.Result.ComplexMessage.ErrorMessages.Add(errorMsg);
                           ErrorStoreSampleDys.Add(notHasPurchaseOrderStoreSamplesOrderDy);
                           continue;
                       }
                       else
                       {
                           sucessOrderDys.Add(notHasPurchaseOrderStoreSamplesOrderDy);
                       }
                   }
                   
               }
            }

            
            StoreSampleOrderDyAssociatePurchaseOrderSubmitHq(sucessOrderDys.ToArray());
        }

        /// <summary>
        /// 处理销售合同为【门店上样】的采购订单提交、审核，以及提交总部
        /// </summary>
        /// <param name="storeSamplesOrderDys"></param>
        private void StoreSampleOrderDyAssociatePurchaseOrderSubmitHq(DynamicObject[] storeSamplesOrderDys)
        {
            if (storeSamplesOrderDys == null || !storeSamplesOrderDys.Any()) return;

            //找到对应的销售合同的单据编号
            var orderNums = storeSamplesOrderDys.Select(x => Convert.ToString(x["fbillno"]));

            var sqlStr = $@" select typ.fid as 'billhead_id',
                                    typoe.fsourcebillno as 'sourceorderno'
                                   from t_ydj_purchaseorder typ with(nolock )
                                    inner join t_ydj_poorderentry typoe with(nolock ) on typ.fid = typoe.fid
                            where typ.fmainorgid = '{this.Context.Company}' 
                                    and typ.fcancelstatus = '0'
                                    and typoe.fsourceformid = 'ydj_order'
                                    and typoe.fsourcebillno in ({string.Join(",", orderNums.Select(x => $"'{x}'"))})
                        ";

            var tempPurChaseOrderIdDys = DBService.ExecuteDynamicObject(this.Context, sqlStr);

            if (tempPurChaseOrderIdDys != null && tempPurChaseOrderIdDys.Any())
            {
                var purChaseOrderIdList = tempPurChaseOrderIdDys.Select(x=>Convert.ToString(x["billhead_id"])).ToList();
                
                var purChaseOrderDys = this.Context.LoadBizDataById("ydj_purchaseorder", purChaseOrderIdList);

                if (purChaseOrderDys != null && purChaseOrderDys.Any())
                {
                    foreach (var purChaseOrderDy in purChaseOrderDys)
                    {
                        var purchaseOrderId = Convert.ToString(purChaseOrderDy["id"]);
                        var findOrderNoList = tempPurChaseOrderIdDys
                                                                    .Where(x => Convert.ToString(x["billhead_id"]).EqualsIgnoreCase(purchaseOrderId))
                                                                    .Select(y => Convert.ToString(y["sourceorderno"]))
                                                                    .Distinct()
                                                                    .ToList();
                        var findStoreSampleOrderDys = new List<DynamicObject>();
                        if (findOrderNoList != null && findOrderNoList.Any())
                        {
                            findStoreSampleOrderDys = storeSamplesOrderDys.Where(x=>findOrderNoList.Any(y=>y.Equals(Convert.ToString(x["fbillno"])))).ToList();
                        }
                        var firstStoreSampleOrderDy = findStoreSampleOrderDys.First();
                        if (firstStoreSampleOrderDy != null)
                        {
                            //采购员取销售合同上的销售员
                            purChaseOrderDy["fpostaffid"] = firstStoreSampleOrderDy["fstaffid"];
                        }
                        
                        var storeId = Convert.ToString(purChaseOrderDy["fstoreid"]);
                        
                        if (storeId.IsNullOrEmptyOrWhiteSpace())
                        {
                            var poDeptId = Convert.ToString(purChaseOrderDy["fpodeptid"]);

                            var deptDy = this.Context.LoadBizBillHeadDataById("ydj_dept", poDeptId, "fstore");

                            if (deptDy != null)
                            {
                                var deptStoreId = Convert.ToString(deptDy["fstore"]);
                                purChaseOrderDy["fstoreid"] = deptStoreId;
                            }
                        }
                        
                        SetPurchaseOrderPriceZeroWhenDirectStoreSampleOrderPushPurchaseOrder(purChaseOrderDy);
                        
                        var option = new Dictionary<string, object>();
                        option.Add("autoaudit", true);
                        option.Add("IgnoreCheckPermssion", true);
                        var operResult = Gateway.InvokeBillOperation(this.Context, "ydj_purchaseorder", new[] {purChaseOrderDy}, "save", option);
                        if (!operResult.IsSuccess || operResult.ComplexMessage.ErrorMessages.Any() ||
                            operResult.ComplexMessage.WarningMessages.Any())
                        {
                            var entities = purChaseOrderDy["fentity"] as DynamicObjectCollection;
                            var sourceOrderNos = entities.Where(x =>
                                    Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_order") &&
                                    !Convert.ToString(x["fsourcebillno"]).IsNullOrEmptyOrWhiteSpace())
                                .Select(x => Convert.ToString(x["fsourcebillno"]))
                                .ToList();
                            var findStoreDys = findStoreSampleOrderDys.Where(x =>
                                sourceOrderNos.Any(y => Convert.ToString(x["fbillno"]).EqualsIgnoreCase(y))).ToList();
                            var joinErrorStr = string.Join(",", operResult.ComplexMessage.ErrorMessages.Select(x => x));
                            this.Result.ComplexMessage.ErrorMessages.Add($"门店上样的销售合同{string.Join("、", findStoreDys.Select(x => $"【{Convert.ToString(x["fbillno"])}】"))}对应的采购订单【{Convert.ToString(purChaseOrderDy["fbillno"])}】审核失败失败，{joinErrorStr}");
                            this.ErrorStoreSampleDys.AddRange(findStoreSampleOrderDys);
                            continue;
                        }

                        option.Add("__fromdirectordersubmithqstoresimple__", "__fromdirectordersubmithqstoresimple__");
                        var submitHqResult = Gateway.InvokeBillOperation(this.Context, "ydj_purchaseorder", new[] {purChaseOrderDy}, "submithq", option);
                        if (!submitHqResult.IsSuccess || submitHqResult.ComplexMessage.ErrorMessages.Any() || submitHqResult.ComplexMessage.WarningMessages.Any())
                        {
                            var entities = purChaseOrderDy["fentity"] as DynamicObjectCollection;
                            var sourceOrderNos = entities.Where(x =>
                                    Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_order") &&
                                    !Convert.ToString(x["fsourcebillno"]).IsNullOrEmptyOrWhiteSpace())
                                .Select(x => Convert.ToString(x["fsourcebillno"]))
                                .ToList();
                            var findStoreDys = findStoreSampleOrderDys.Where(x =>
                                sourceOrderNos.Any(y => Convert.ToString(x["fbillno"]).EqualsIgnoreCase(y))).ToList();
                            var joinErrorStr = string.Join(",",
                                submitHqResult.ComplexMessage.ErrorMessages.Select(x => x));
                            this.Result.ComplexMessage.ErrorMessages.Add($"门店上样的销售合同{string.Join("、", findStoreDys.Select(x => $"【{Convert.ToString(x["fbillno"])}】"))}对应的采购订单【{Convert.ToString(purChaseOrderDy["fbillno"])}】提交总部失败，{joinErrorStr}");
                            this.ErrorStoreSampleDys.AddRange(findStoreSampleOrderDys);
                            continue;
                        }
                        
                        //采购订单提交总部时间
                        var hqderDate = Convert.ToDateTime(purChaseOrderDy["fhqderdate"]);

                        //采购订单总部合同状态 '01':'新建','02':'提交至总部','03':'已终审','04':'排产中','05':'驳回'
                        var hqderStatus = Convert.ToString(purChaseOrderDy["fhqderstatus"]);

                        foreach (var storeSamplesOrderDy in findStoreSampleOrderDys)
                        {
                            storeSamplesOrderDy["fsubmithtime"] = hqderDate;

                            // '1':'已提交总部','2':'已驳回','3':'已终审'
                            storeSamplesOrderDy["fchstatus"] = "1";
                        }

                        this.Context.SaveBizData("ydj_order", findStoreSampleOrderDys);
                        
                    }
                    
                }
            }

        }

        /// <summary>
        /// 设置当是直营的时候，门店上样销售合同转采购订单的时候，采购订单的单价、成交单件为0
        /// </summary>
        /// <param name="purchaseOrder"></param>
        private void SetPurchaseOrderPriceZeroWhenDirectStoreSampleOrderPushPurchaseOrder(DynamicObject purchaseOrder)
        {
            var productPurEntrys = purchaseOrder["fentity"] as DynamicObjectCollection;
            foreach (var productPurEntry in productPurEntrys)
            {
                //采购单价
                productPurEntry["fprice"] = 0M;

                //采购金额
                productPurEntry["famount"] = Convert.ToDecimal(productPurEntry["fprice"]) * Convert.ToDecimal(productPurEntry["fbizqty"]);

                //成交单价
                productPurEntry["fdealprice"] = 0M;

                //成交金额
                productPurEntry["fdealamount"] = Convert.ToDecimal(productPurEntry["fdealprice"]) * Convert.ToDecimal(productPurEntry["fbizqty"]);

                //折扣额
                productPurEntry["fdistamount"] = Convert.ToDecimal(productPurEntry["famount"]) - Convert.ToDecimal(productPurEntry["fdealamount"]);

                //保留2位小数成交单价
                productPurEntry["rounded_fdealprice"] = 0M;

                //国补尾差
                productPurEntry["rounded_fdealamount"] = 0M;

                if (Convert.ToDecimal(productPurEntry["fprice"]) <= 0)
                {
                    productPurEntry["fdistrate"] = 0M;
                }
            }

            var purchaseOrderService = this.Context.Container.GetService<IPurchaseOrderService>();

            purchaseOrderService.CalculateSettlement(this.Context, purchaseOrder);
        }

        /// <summary>
        /// 设置销售合同明细行的佣金金额与比例
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orderDys">销售合同数据包</param>
        private void SetDirectOrderProductEntryCommissionAmount(UserContext userCtx, DynamicObject[] orderDys)
        {
            var orderService = userCtx.Container.GetService<IOrderService>();
            foreach (var orderDy in orderDys)
            {
                orderService.CalculateCommissionAmountAndRate(userCtx, orderDy);
            }
        }

    }
}
