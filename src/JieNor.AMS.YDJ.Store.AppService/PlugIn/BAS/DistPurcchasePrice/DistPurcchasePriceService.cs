using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 二级分销采购价目表计算
    /// </summary>
    [InjectService]
    public class DistPurchasePriceService
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        [InjectProperty]
        protected IDBService dbService { get; set; }

        public IOperationResult PriceCalculate(UserContext userCtx, OperateOption option)
        {
            var result = userCtx.Container.GetService<IOperationResult>();
            // 通过临时表生成数据
            var tmpTableName = string.Empty;
            var table = string.Empty;
            DataTable dt = new DataTable();
            try
            {
                DynamicObjectCollection data = null;
                tmpTableName = dbService.CreateTemporaryTableName(userCtx);
                //1： 授权商品数据
                var authPara = new DataQueryRuleParaInfo();
                var tempView = userCtx.GetAuthProductDataPKID(authPara);

                //2： 获取二级分销商对应的客户档案id
                string customerSQL = $"select top 1 fid from t_ydj_customer where forgid ='{userCtx.Company}' and fmainorgid='{userCtx.ParentCompanyId}'";
                var customerId = dbService.ExecuteDynamicObject(userCtx, customerSQL).ToList()?.FirstOrDefault()?["fid"].ToString();

                //3: 获取二级分销商计价规则以及各系列的比例
                var currAgentSql = $@"
                select t1.fresellratio,t1.fpricerule,t2.fseriesid,t2.fresellratio fresellratio_e
				from t_bas_agent t1 with(nolock)
				left join t_agent_ratioinfo t2  with(nolock) on t1.fid=t2.fid
                where t1.fid='{userCtx.Company}'";
                var agentData = userCtx.ExecuteDynamicObject(currAgentSql, null);
                if (agentData == null) throw new Exception("经销商不存在");
                var RatioData = agentData?
                                    .Where(x => x != null &&
                                                x["fseriesid"] != null &&
                                                x["fresellratio_e"] != null &&
                                                !string.IsNullOrWhiteSpace(x["fseriesid"].ToString()) &&
                                                !string.IsNullOrWhiteSpace(x["fresellratio_e"].ToString()))
                                    .Select(x => new {
                                        fseriesid = x["fseriesid"].ToString(),
                                        fresellratio = x["fresellratio_e"] != null ? Convert.ToDecimal(x["fresellratio_e"].ToString()) : 1m
                                    })
                                    .ToList();
                string pricerule = Convert.ToString(agentData.FirstOrDefault()?["fpricerule"]);
                decimal defaultRatio = Convert.ToDecimal(agentData.FirstOrDefault()?["fresellratio"]??1M);
                if (pricerule.IsNullOrEmptyOrWhiteSpace()) { throw new Exception("未维护二级分销合同价格计算规则"); };

                //4 插入数据到临时表
                InsertDataToTempTable(userCtx, tmpTableName, tempView, customerId, pricerule);

                //5 取出数据
                var querySql = $@"/*dialect*/select * from {tmpTableName} as t0 with(nolock)";
                data = dbService.ExecuteDynamicObject(userCtx, querySql);
                var addData = data.Select(x => new
                {
                    fnumber = x["fnumber"].ToString(),
                    fformid = x["fformid"].ToString(),
                    fmaterialid = x["fmaterialid"].ToString(),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    funitid = x["funitid"].ToString(),
                    funstdtype = x["funstdtype"].ToString(),
                    fqty = x["fqty"].ToString(),
                    fstartdate = x["fstartdate"].ToString(),
                    fexpiredate = x["fexpiredate"].ToString(),
                    fprice = Convert.ToDecimal(x["fprice"].ToString()) * 1M,
                    fseriesid = x["fseriesid"].ToString()
                }).ToList();
                var saveDatas = (from leftItem in addData
                                 join rightItem in RatioData
                                 on leftItem.fseriesid equals rightItem.fseriesid into joinedItems
                                 from rightItem in joinedItems.DefaultIfEmpty()
                                 select new
                                 {
                                     fnumber = leftItem.fnumber,
                                     fformid = leftItem.fformid,
                                     fmaterialid = leftItem.fmaterialid,
                                     fattrinfo_e = leftItem.fattrinfo_e,
                                     funitid = leftItem.funitid,
                                     funstdtype = leftItem.funstdtype,
                                     fqty = leftItem.fqty,
                                     fstartdate = leftItem.fstartdate,
                                     fexpiredate = leftItem.fexpiredate,
                                     fprice = leftItem.fformid!= "ydj_reprice" ? (rightItem != null ? leftItem.fprice * rightItem.fresellratio : leftItem.fprice * defaultRatio) : leftItem.fprice,
                                 }).ToList();

                dt.Columns.Add("fproductid", typeof(string));
                dt.Columns.Add("fdispurprice", typeof(decimal));
                dt.Columns.Add("fsourcebillno", typeof(string));
                dt.Columns.Add("fsourceformid", typeof(string));
                dt.Columns.Add("fstartdate", typeof(DateTime));
                dt.Columns.Add("fexpiredate", typeof(DateTime));
                dt.Columns.Add("funstdtype", typeof(string));
                dt.Columns.Add("funitid", typeof(string));
                dt.Columns.Add("fqty", typeof(string));
                dt.Columns.Add("fattrinfo_e", typeof(string));
                dt.BeginLoadData();
                var taskCount = 1;
                if (Environment.ProcessorCount > 1)
                {
                    taskCount = Environment.ProcessorCount - 1;
                }
                if (taskCount > 4)
                {
                    taskCount = 4;
                }
                ParallelOptions parallelOptions = new ParallelOptions();
                parallelOptions.MaxDegreeOfParallelism = taskCount;
                Parallel.ForEach(saveDatas, parallelOptions, item =>
                {
                    var startDate = item.fstartdate;
                    var expireDate = item.fexpiredate;
                    string fproductid = item.fmaterialid;
                    decimal fprice = item.fprice;
                    string fsourcebillno = item.fnumber;
                    string fsourceformid = item.fformid;
                    string funstdtype = item.funstdtype;
                    string funitid = item.funitid;
                    string fqty = item.fqty;
                    string fattrinfo_e = item.fattrinfo_e;
                    lock (dt.Rows)
                    {
                        dt.Rows.Add(fproductid, fprice, fsourcebillno, fsourceformid,
                            startDate, expireDate, funstdtype, funitid, fqty, fattrinfo_e);
                    }
                });
                dt.EndLoadData();
                table = dbService.CreateTempTableWithDataTable(userCtx, dt, 2000);
                //创建索引
                try
                {
                    var idxName = "idx_" + table;
                    var indexSql = @" create index {0} on {1}(fproductid ,fattrinfo_e);".Fmt(idxName, table);
                    this.DBServiceEx.Execute(userCtx, indexSql);
                }
                catch (Exception) { }

                //删除不在临时表的数据
                string dleteSql = $@"/*dialect*/delete t from t_ydj_distpurchaseprice t
                    where not exists(select 1 from {table} t2 with(nolock) where t.fproductid=t2.fproductid and t.fattrinfo_e=t2.fattrinfo_e)
                    and t.fmainorgid='{userCtx.Company}'";
                this.DBServiceEx.Execute(userCtx, dleteSql);

                //更新存在的新数据
                string updateSql = $@"/*dialect*/
                    update t set t.fdispurprice=t2.fdispurprice, t.fsourcebillno=t2.fsourcebillno, t.fsourceformid=t2.fsourceformid, t.fstartdate=t2.fstartdate,
					t.fexpiredate=t2.fexpiredate, t.funstdtype=t2.funstdtype,t.fmodifierid='{userCtx.UserId}',t.fupdatetime=getdate()
                    from t_ydj_distpurchaseprice t with(nolock) 
                    inner join {table} t2 with(nolock)
					on t.fproductid=t2.fproductid  and t.fmainorgid='{userCtx.Company}'
                    and t.fattrinfo_e=t2.fattrinfo_e 
                    where (t.fdispurprice<>t2.fdispurprice or t.fsourceformid<>t2.fsourceformid or t.fsourcebillno<>t2.fsourcebillno) ";
                this.DBServiceEx.Execute(userCtx, updateSql);

                //新增增量数据
                string insertSql = $@"/*dialect*/insert into t_ydj_distpurchaseprice(fid,fmainorgid,fpricerule,fproductid,fdispurprice,fsourcebillno,fsourceformid,fstartdate,fexpiredate,funstdtype,funitid,fqty,fattrinfo_e,fconfirmstatus,fupdatetime,fmodifierid)
                                                               select LOWER(REPLACE(LTRIM(NEWID()),'-','')) fid, {userCtx.Company} as 'fmainorgid',{pricerule},fproductid, fdispurprice,fsourcebillno,fsourceformid,fstartdate,fexpiredate,funstdtype,funitid,fqty,fattrinfo_e,2,getdate() as 'fupdatetime','{userCtx.UserId}' as  'fmodifierid'  
                                                                from {table}  t with(nolock)
                                                               where not exists(select 1 from t_ydj_distpurchaseprice t2 with(nolock) 
                                        where t.fproductid=t2.fproductid and  t2.fattrinfo_e = t.fattrinfo_e  and t2.fmainorgid='{userCtx.Company}')";
                this.DBServiceEx.Execute(userCtx, insertSql);


                result.SimpleMessage = "分销采购价目更新成功！";
                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.SimpleMessage = $"分销采购价目更新异常,{ex.Message}";
                return result;

            }
            finally
            {
                dbService.DeleteTempTableByName(userCtx, tmpTableName, true);
                if (!table.IsNullOrEmptyOrWhiteSpace())
                {
                    dbService.DeleteTempTableByName(userCtx, table, true);
                }
                dt.Clear();
                dt.Dispose();
                dt = null;
            }

        }
        private void InsertDataToTempTable(UserContext userCtx, string tmpTableName, string tempView, string customerId, string pricerule)
        {
            InsertRePriceData(userCtx, tmpTableName, tempView, customerId);
            if (pricerule.EqualsIgnoreCase("0"))
            {
                InsertPurchasePrice(userCtx, tmpTableName);
                InsertSelfPurchasePrice(userCtx, tmpTableName);
            }
            if (pricerule.EqualsIgnoreCase("1"))
            {
                InsertPriceData(userCtx, tmpTableName, tempView, customerId);
            }
            InsertSynthesizePriceData(userCtx, tmpTableName, tempView, pricerule);
        }

        /// <summary>
        /// 插入分销销售价目(根据商品, 辅助属性 ,单位 分组获取分销销售价目数据(根据排序获取每组第一条))
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tmpTableName"></param>
        /// <param name="tempView"></param>
        /// <param name="customerId"></param>
        private void InsertRePriceData(UserContext userCtx, string tmpTableName, string tempView, string customerId)
        {
            var insertSql = $@" /*dialect*/select * into {tmpTableName} from 
                               ( 
                                  SELECT fnumber,  fformid,fmaterialid,  fattrinfo_e,funitid, funstdtype, fqty, fstartdate, fexpiredate,fprice,fseriesid FROM 
                               ( 
                                     SELECT p.fnumber, p.fformid,pe.fproductid as fmaterialid, pe.fattrinfo_e,pe.funitid, pe.funstdtype,pe.fqty,pe.fstartdate, 
                                        pe.fexpiredate, pe.fsalprice AS fprice,m.fseriesid,
                                        CASE 
                                            WHEN pe.fstartdate IS NULL OR pe.fexpiredate IS NULL THEN 1
                                            WHEN GETDATE() BETWEEN pe.fstartdate AND pe.fexpiredate THEN 1
                                            ELSE 0 
                                        END AS enable,
                                        ce.fcustomerid,
                                        ROW_NUMBER() OVER (
                                            PARTITION BY pe.fproductid, pe.funitid, pe.fattrinfo_e 
                                            ORDER BY 
                                                CASE 
                                                    WHEN pe.fstartdate IS NULL OR pe.fexpiredate IS NULL THEN 1
                                                    WHEN GETDATE() BETWEEN pe.fstartdate AND pe.fexpiredate THEN 1
                                                    ELSE 0 
                                                END DESC,
                                                ce.fcustomerid DESC,
                                                pe.fstartdate DESC
                                        ) AS RowNum
                                    FROM   t_ydj_priceentry pe WITH(NOLOCK) 
                                     INNER JOIN t_bd_material m WITH(NOLOCK) ON pe.fproductid = m.fid AND pe.fconfirmstatus = '2' 
                                    INNER JOIN t_ydj_price p WITH(NOLOCK) ON p.fid = pe.fid AND p.fmainorgid IN ('{userCtx.ParentCompanyId}','{userCtx.TopCompanyId}') 
                                   AND p.FFormId ='ydj_reprice' AND p.fforbidstatus = '0' AND p.ftype='quote_type_04'
                                    OUTER APPLY (
                                        SELECT TOP 1 ce.fcustomerid
                                        FROM t_ydj_customerentry ce WITH(NOLOCK)
                                        WHERE ce.fid = p.fid
                                        AND
                                        (
                                            (p.flimit = '' AND ce.fcustomerid IS NULL) OR
                                            (p.flimit = 'limit_01' AND ce.fcustomerid = '{customerId}')
                                        )
                                    ) ce
                                    WHERE (m.fsalunitid = pe.funitid OR pe.funitid = '') and pe.fproductid in({tempView})
                                      ) as t  WHERE RowNum = 1
                                            ) tt";
            dbService.ExecuteDynamicObject(userCtx, insertSql);
        }
        /// <summary>
        /// 插入销售价目数据(根据商品, 辅助属性 ,单位 分组获取销售价目数据(根据排序获取每组第一条))
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tmpTableName"></param>
        /// <param name="productview"></param>
        private void InsertPriceData(UserContext userCtx, string tmpTableName, string productview, string customerId)
        {
            var insertSql = $@" /*dialect*/insert into {tmpTableName}(fnumber,fformid,fmaterialid,fattrinfo_e,funitid, funstdtype, fqty, fstartdate,fexpiredate,fprice,fseriesid)
                               ( 
                                  SELECT fnumber,  fformid,fmaterialid,  fattrinfo_e,funitid, funstdtype, fqty, fstartdate, fexpiredate,fprice,fseriesid FROM 
                               ( 
                                     SELECT p.fnumber, p.fformid,pe.fproductid as fmaterialid, pe.fattrinfo_e,pe.funitid, pe.funstdtype,pe.fqty,pe.fstartdate, 
                                        pe.fexpiredate, pe.fsalprice AS fprice,m.fseriesid,
                                        CASE 
                                            WHEN pe.fstartdate IS NULL OR pe.fexpiredate IS NULL THEN 1
                                            WHEN GETDATE() BETWEEN pe.fstartdate AND pe.fexpiredate THEN 1
                                            ELSE 0 
                                        END AS enable,
                                        ce.fcustomerid,
                                        ROW_NUMBER() OVER (
                                            PARTITION BY pe.fproductid, pe.funitid, pe.fattrinfo_e 
                                            ORDER BY 
                                                CASE 
                                                    WHEN pe.fstartdate IS NULL OR pe.fexpiredate IS NULL THEN 1
                                                    WHEN GETDATE() BETWEEN pe.fstartdate AND pe.fexpiredate THEN 1
                                                    ELSE 0 
                                                END DESC,
                                                ce.fcustomerid DESC,
                                                pe.fstartdate DESC
                                        ) AS RowNum
                                    FROM   t_ydj_priceentry pe WITH(NOLOCK) 
                                     INNER JOIN t_bd_material m WITH(NOLOCK) ON pe.fproductid = m.fid AND pe.fconfirmstatus = '2' 
                                    INNER JOIN t_ydj_price p WITH(NOLOCK) ON p.fid = pe.fid AND p.fmainorgid IN ('{userCtx.ParentCompanyId}','{userCtx.TopCompanyId}') AND p.FFormId ='ydj_price' AND p.fforbidstatus = '0' AND p.ftype='quote_type_01'
                                    OUTER APPLY (
                                        SELECT TOP 1 ce.fcustomerid
                                        FROM t_ydj_customerentry ce WITH(NOLOCK)
                                        WHERE ce.fid = p.fid
                                        AND
                                        (
                                            (p.flimit = '' AND ce.fcustomerid IS NULL) OR
                                            (p.flimit = 'limit_01' AND ce.fcustomerid = '{customerId}')
                                        )
                                    ) ce
                                    WHERE (m.fsalunitid = pe.funitid OR pe.funitid = '') and pe.fproductid in({productview})
                                          and not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=pe.fproductid and t4.fattrinfo_e=pe.fattrinfo_e and t4.funitid=pe.funitid)
                                      ) as t  WHERE RowNum = 1)
                                            ";
            dbService.ExecuteDynamicObject(userCtx, insertSql);
        }

        /// <summary>
        /// 插入采购价目数据(根据商品, 辅助属性 ,单位 分组获取销售价目数据(根据排序获取每组第一条))
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tmpTableName"></param>
        private void InsertPurchasePrice(UserContext userCtx, string tmpTableName)
        {
            var insertSql = $@"    /*dialect*/insert into {tmpTableName}(fnumber,fformid,fmaterialid,fattrinfo_e,funitid, funstdtype, fqty, fstartdate,fexpiredate,fprice,fseriesid)
                (select fnumber,  fformid,fmaterialid,  fattrinfo_e,funitid, funstdtype, fqty, fstartdate, fexpiredate,fprice ,fseriesid from (
                   select   p.fnumber, p.fformid, pe.fproductid_e as fmaterialid, pe.fattrinfo_e, pe.funitid_e as  funitid,'' as funstdtype,  '1' as fqty, 
                    pe.fstartdate_e as fstartdate, pe.fexpiredate_e as fexpiredate, pe.fpurprice as fprice,m.fseriesid,
                              ROW_NUMBER() OVER (
                                            PARTITION BY pe.fproductid_e, pe.funitid_e, pe.fattrinfo_e 
                                            ORDER BY  pe.fstartdate_e desc  ) AS RowNum
                    from t_ydj_purchasepriceentry pe with(nolock)
                    inner join t_ydj_purchaseprice p with(nolock) on p.fid=pe.fid 
                    inner join t_bd_material m with(nolock) on pe.fproductid_e=m.fid 
                    where p.fmainorgid in ('{userCtx.ParentCompanyId}','{userCtx.TopCompanyId}')
                    and m.fmainorgid='{userCtx.TopCompanyId}'
                    and p.fformid='ydj_purchaseprice'
                    and p.fforbidstatus='0' 
                    and pe.fconfirmstatus='2' 
                    and (m.fpurunitid=pe.funitid_e or pe.funitid_e='') 
                    and (case when pe.fstartdate_e is null then 1 else case when datediff(d,pe.fstartdate_e,GETDATE())>-1 then 1 else 0 end end)=1 
                    and (case when pe.fexpiredate_e is null then 1 else case when datediff(d,GETDATE(),pe.fexpiredate_e)>-1 then 1 else 0 end end)=1 
                    and not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=pe.fproductid_e and t4.fattrinfo_e=pe.fattrinfo_e and t4.funitid=pe.funitid_e)
                    )t
                    where  RowNum = 1
                       ) ";
            dbService.ExecuteDynamicObject(userCtx, insertSql);
        }

        /// <summary>
        /// 插入自建价目数据(根据商品, 辅助属性 ,单位 分组获取销售价目数据(根据排序获取每组第一条))
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tmpTableName"></param>
        private void InsertSelfPurchasePrice(UserContext userCtx, string tmpTableName)
        {
            var insertSql = $@"    /*dialect*/insert into {tmpTableName}(fnumber,fformid,fmaterialid,fattrinfo_e,funitid, funstdtype, fqty, fstartdate,fexpiredate,fprice,fseriesid)
                (select fnumber,  fformid,fmaterialid,  fattrinfo_e,funitid, funstdtype, fqty, fstartdate, fexpiredate,fprice ,fseriesid from (
                   select   p.fnumber, p.fformid, pe.fproductid_e as fmaterialid, pe.fattrinfo_e, pe.funitid_e as  funitid,'' as funstdtype,  '1' as fqty, 
                    pe.fstartdate_e as fstartdate, pe.fexpiredate_e as fexpiredate, pe.fpurprice as fprice,m.fseriesid,
                              ROW_NUMBER() OVER (
                                            PARTITION BY pe.fproductid_e, pe.funitid_e, pe.fattrinfo_e 
                                            ORDER BY  pe.fstartdate_e desc  ) AS RowNum
                    from t_ydj_purchasepriceentry pe with(nolock)
                    inner join t_ydj_purchaseprice p with(nolock) on p.fid=pe.fid 
                    inner join t_bd_material m with(nolock) on pe.fproductid_e=m.fid 
                    where p.fmainorgid in ('{userCtx.ParentCompanyId}','{userCtx.TopCompanyId}')
                    and m.fmainorgid='{userCtx.ParentCompanyId}'
                    and p.fformid='ydj_selfpurchaseprice'
                    and p.fforbidstatus='0' 
                    and pe.fconfirmstatus='2' 
                    and (m.fpurunitid=pe.funitid_e or pe.funitid_e='') 
                    and (case when pe.fstartdate_e is null then 1 else case when datediff(d,pe.fstartdate_e,GETDATE())>-1 then 1 else 0 end end)=1 
                    and (case when pe.fexpiredate_e is null then 1 else case when datediff(d,GETDATE(),pe.fexpiredate_e)>-1 then 1 else 0 end end)=1 
                    and not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=pe.fproductid_e and t4.fattrinfo_e=pe.fattrinfo_e and t4.funitid=pe.funitid_e)
                    )t
                    where  RowNum = 1
                       )";
            dbService.ExecuteDynamicObject(userCtx, insertSql);
        }
        /// <summary>
        ///  插入综合价目表数据(根据商品, 辅助属性 ,单位 分组获取综合价目数据(根据排序获取每组第一条))
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tmpTableName"></param>
        /// <param name="productview"></param>
        /// <param name="priceRule"></param>
        private void InsertSynthesizePriceData(UserContext userCtx, string tmpTableName, string productview, string priceRule)
        {
            string priceFeild = "fpurfacprice";
            if (priceRule == "1")
            {
                priceFeild = "funifysaleprice";
            }
            var insertSql = $@"/*dialect*/insert into {tmpTableName}(fnumber,fformid,fmaterialid,fattrinfo_e,funitid, funstdtype, fqty, fstartdate,fexpiredate,fprice,fseriesid)
                              (select fnumber,  fformid,fmaterialid,  fattrinfo_e,funitid, funstdtype, fqty, fstartdate, fexpiredate,fprice,fseriesid from  (
           select '' as fnumber,tab1.fformid,fmaterialid,tab1.fattrinfo_e,tab1.funitid,'' as funstdtype, '1' as fqty,getdate() as fstartdate,'2099-01-01 00:00:00' AS fexpiredate, 
                      {priceFeild}  as fprice,m.fseriesid,
                                 ROW_NUMBER() OVER (
                                            PARTITION BY tab1.fmaterialid, tab1.funitid, tab1.fattrinfo_e 
                                            ORDER BY  tab1.fupdatetime desc  ) AS RowNum
                                       from t_ydj_pricesynthesize tab1
                                       inner join t_bd_material m with(nolock) on tab1.fmaterialid=m.fid 
                                 where  tab1.fmaterialid in ({productview}) and tab1.fmainorgid='{userCtx.ParentCompanyId}' and {priceFeild}>0 and not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=tab1.fmaterialid and t4.fattrinfo_e=tab1.fattrinfo_e and t4.funitid=tab1.funitid)) t where  RowNum = 1 )
                                ";
            dbService.ExecuteDynamicObject(userCtx, insertSql);
        }
    }
}
