using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse
{
    /// <summary>
    /// 收支记录帮助类
    /// </summary>
    public class IncomeDisburseHelper
    {
        /// <summary>
        /// 查找源单总的收退款金额
        /// </summary>
        /// <param name="sourceReceiptRefunds">源单收退款金额</param>
        /// <param name="sourceId">源单ID</param>
        /// <param name="incomeType">收支类型</param>
        public static decimal FindSourceReceiptRefundAmount(
            DynamicObjectCollection sourceReceiptRefunds,
            string sourceId,
            string incomeType)
        {
            switch (incomeType)
            {
                case IncomeTypeConsts.receiptConfirmed:

                    // 源单总的确认收款
                    var receiptConfirmedSum = Convert.ToDecimal(sourceReceiptRefunds
                        ?.FirstOrDefault(o =>
                        {
                            return Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                            && Convert.ToString(o["ftype"]).EqualsIgnoreCase(IncomeTypeConsts.receiptConfirmed);
                        })
                        ?["famount"] ?? 0);
                    return receiptConfirmedSum;

                case IncomeTypeConsts.receiptUnConfirmed:

                    // 源单总的待确认收款
                    var receiptUnConfirmedSum = Convert.ToDecimal(sourceReceiptRefunds
                        ?.FirstOrDefault(o =>
                        {
                            return Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                            && Convert.ToString(o["ftype"]).EqualsIgnoreCase(IncomeTypeConsts.receiptUnConfirmed);
                        })
                        ?["famount"] ?? 0);
                    return receiptUnConfirmedSum;

                case IncomeTypeConsts.refundConfirmed:

                    // 源单总的确认退款
                    var refundConfirmedSum = Convert.ToDecimal(sourceReceiptRefunds
                        ?.FirstOrDefault(o =>
                        {
                            return Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                            && Convert.ToString(o["ftype"]).EqualsIgnoreCase(IncomeTypeConsts.refundConfirmed);
                        })
                        ?["famount"] ?? 0);
                    return refundConfirmedSum;

                case IncomeTypeConsts.refundUnConfirmed:

                    // 源单总的待确认退款
                    var refundUnConfirmedSum = Convert.ToDecimal(sourceReceiptRefunds
                        ?.FirstOrDefault(o =>
                        {
                            return Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                            && Convert.ToString(o["ftype"]).EqualsIgnoreCase(IncomeTypeConsts.refundUnConfirmed);
                        })
                        ?["famount"] ?? 0);
                    return refundUnConfirmedSum;

                default:
                    return 0;
            }
        }

        /// <summary>
        /// 加载源单单据关联的指定收支类型对应的收支总金额
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="sourceFormId">源单表单标识</param>
        /// <param name="fids">本次审核收支记录ID集合</param>
        /// <param name="sourceIds">源单ID集合</param>
        /// <param name="incomeTypes">收支类型</param>
        /// <returns></returns>
        public static DynamicObjectCollection LoadOrderReceiptRefundAmount(
            UserContext userCtx,
            string sourceFormId,
            List<string> sourceIds,
            List<string> fids,
            List<string> incomeTypes)
        {
            if (incomeTypes == null || !incomeTypes.Any())
            {
                throw new ArgumentException($"收支类型参数 {nameof(incomeTypes)} 为空，请检查！");
            }
            foreach (var incomeType in incomeTypes)
            {
                if (!IncomeTypeConsts.IncomeTypes.Contains(incomeType))
                {
                    throw new ArgumentOutOfRangeException($"收支类型参数值 {incomeType} 不在有效的范围，请检查！");
                }
            }

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fsourceformid", System.Data.DbType.String, sourceFormId)
            };

            var sourceIdWhere = "";
            if (sourceIds.Count == 1)
            {
                sourceIdWhere = $" and fcancelstatus = 0  and fsourceid='{sourceIds[0]}'";
            }
            else
            {
                sourceIdWhere = $" and fcancelstatus = 0  and fsourceid in('{string.Join("','", sourceIds)}')";
            }

            //本次审核已确认的单据排除不在查询范围内
            var IdWhere = "";
            if (fids != null)
            {
                if (fids.Count == 1)
                {
                    IdWhere = $" and fid!='{fids[0]}'";
                }
                else
                {
                    IdWhere = $" and fid not in('{string.Join("','", fids)}')";
                }
            }

            var sqlList = new List<string>();

            if (incomeTypes.Any(o => o.EqualsIgnoreCase(IncomeTypeConsts.receiptConfirmed)))
            {
                sqlList.Add($@"
                select fsourceid,'{IncomeTypeConsts.receiptConfirmed}' ftype,isnull(sum(famount),0) famount from t_coo_incomedisburse  
                where fmainorgid=@fmainorgid and fsourceformid=@fsourceformid {sourceIdWhere}{IdWhere} and fpurpose='bizpurpose_02' and fbizstatus='bizstatus_02'
                group by fsourceid");
            }

            if (incomeTypes.Any(o => o.EqualsIgnoreCase(IncomeTypeConsts.receiptUnConfirmed)))
            {
                sqlList.Add($@"
                select fsourceid,'{IncomeTypeConsts.receiptUnConfirmed}' ftype,isnull(sum(famount),0) famount from t_coo_incomedisburse 
                where fmainorgid=@fmainorgid and fsourceformid=@fsourceformid {sourceIdWhere} and fpurpose='bizpurpose_02' and fbizstatus='bizstatus_01'
                group by fsourceid");
            }

            if (incomeTypes.Any(o => o.EqualsIgnoreCase(IncomeTypeConsts.refundConfirmed)))
            {
                sqlList.Add($@"
                select fsourceid,'{IncomeTypeConsts.refundConfirmed}' ftype,isnull(sum(famount),0) famount from t_coo_incomedisburse  
                where fmainorgid=@fmainorgid and fsourceformid=@fsourceformid {sourceIdWhere}{IdWhere} and fpurpose='bizpurpose_06' and fbizstatus='bizstatus_02'
                group by fsourceid");
            }

            if (incomeTypes.Any(o => o.EqualsIgnoreCase(IncomeTypeConsts.refundUnConfirmed)))
            {
                sqlList.Add($@"
                select fsourceid,'{IncomeTypeConsts.refundUnConfirmed}' ftype,isnull(sum(famount),0) famount from t_coo_incomedisburse  
                where fmainorgid=@fmainorgid and fsourceformid=@fsourceformid {sourceIdWhere} and fpurpose='bizpurpose_06' and fbizstatus='bizstatus_01'
                group by fsourceid");
            }

            var sqlText = string.Join(" union all ", sqlList);

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);

            return dynObjs;
        }
    }

    /// <summary>
    /// 收支类型常量
    /// </summary>
    public class IncomeTypeConsts
    {
        /// <summary>
        /// 收款已确认
        /// </summary>
        public const string receiptConfirmed = "receiptConfirmed";

        /// <summary>
        /// 收款待确认
        /// </summary>
        public const string receiptUnConfirmed = "receiptUnConfirmed";

        /// <summary>
        /// 退款已确认
        /// </summary>
        public const string refundConfirmed = "refundConfirmed";

        /// <summary>
        /// 退款待确认
        /// </summary>
        public const string refundUnConfirmed = "refundUnConfirmed";

        /// <summary>
        /// 所有的收支类型
        /// </summary>
        public static readonly List<string> IncomeTypes = new List<string> 
        {
            receiptConfirmed,
            receiptUnConfirmed,
            refundConfirmed,
            refundUnConfirmed
        };
    }
}
