using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.PlugIn.FIN.IncomeDisburse
{
    /// <summary>
    /// 收支记录：作废
    /// </summary>
    [InjectService]
    [FormId("coo_incomedisburse")]
    [OperationNo("coocancel")]
    public class CooCancel : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var billNo = this.GetQueryOrSimpleParam("billNo", "");

            if (!billNo.IsNullOrEmptyOrWhiteSpace())
            {
                var bills = this.Context.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new List<string> { billNo });
                if (bills.Any())
                {
                    var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, bills, "Cancel",
                        new Dictionary<string, object>());

                    // 反写源单字段
                    this.BackWriteSourceBill(e.DataEntitys); 

                    this.Result.IsSuccess = false;
                    //this.Result.SimpleMessage = string.Join(",", result.ComplexMessage.ErrorMessages);
                }
            }

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "操作成功！";
        }

        /// <summary>
        /// 反写源单字段
        /// </summary>
        /// <param name="dataEntities"></param>
        private void BackWriteSourceBill(DynamicObject[] dataEntitys)
        {
            var sourceFormIds = new string[] { "ydj_order", "ydj_purchaseorder" };

            // 按源单表单标识分组
            var groups = dataEntitys
                .Where(x =>
                {
                    var sourceFormId = Convert.ToString(x["fsourceformid"]);
                    return !sourceFormId.IsNullOrEmptyOrWhiteSpace()
                    && sourceFormIds.Contains(sourceFormId, StringComparer.OrdinalIgnoreCase)
                    && Convert.ToString(x["fpurpose"]).EqualsIgnoreCase("bizpurpose_02");
                })
                .GroupBy(x => Convert.ToString(x["fsourceformid"]).ToLower());
            if (!groups.Any()) return;

            var dm = this.Container.GetService<IDataManager>();

            foreach (var group in groups)
            {
                var sourceFormId = group.Key;
                var sourceForm = this.MetaModelService?.LoadFormModel(this.Context, sourceFormId);
                dm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));

                // 源单ID集合
                var sourceIds = group
                    .Select(x => Convert.ToString(x["fsourceid"]))
                    .Where(x => !x.IsNullOrEmptyOrWhiteSpace())
                    .Distinct()
                    .ToList();
                if (sourceIds == null || !sourceIds.Any()) continue;

                // 源单数据包
                var sourceBills = dm.Select(sourceIds).OfType<DynamicObject>().ToArray();
                if (sourceBills == null || !sourceBills.Any()) continue;

                // 批量加载源单关联的收支金额
                var sourceReceiptRefunds = IncomeDisburseHelper.LoadOrderReceiptRefundAmount(
                    this.Context,
                    sourceFormId,
                    sourceIds,
                    null,
                    new List<string>
                    {
                        IncomeTypeConsts.receiptUnConfirmed
                    });

                foreach (var incomeDisburse in group)
                {
                    switch (sourceFormId)
                    {
                        case "ydj_order":
                            this.BackWriteOrder(incomeDisburse, sourceBills, sourceReceiptRefunds);
                            break;
                        case "ydj_purchaseorder":
                            this.BackWritePurchaseOrder(incomeDisburse, sourceBills, sourceReceiptRefunds);
                            break;
                    }
                }

                // 保存源单数据
                dm.Save(sourceBills);
            }
        }

        /// <summary>
        /// 反写销售合同
        /// </summary>
        /// <param name="incomeDisburse">收支记录数据包</param>
        /// <param name="sourceBills">销售合同数据包</param>
        /// <param name="sourceReceiptRefunds">销售合同收退款金额</param>
        private void BackWriteOrder(
            DynamicObject incomeDisburse,
            DynamicObject[] sourceBills,
            DynamicObjectCollection sourceReceiptRefunds)
        {
            var sourceId = Convert.ToString(incomeDisburse["fsourceid"]);
            var sourceBill = sourceBills?.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(sourceId));
            if (sourceBill == null) return;

            var receiptUnConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.receiptUnConfirmed);

            this.Logger.WriteLog(this.Context, new LogEntry
            {
                BillIds = incomeDisburse["id"] as string,
                BillNos = incomeDisburse["fbillno"] as string,
                BillFormId = this.HtmlForm.Id,
                OpName = "收支记录作废",
                OpCode = this.OperationNo,
                Content = "执行了【保存】操作，更新销售合同【收款待确认】。销售合同编号：{2}。原值：{0}。新值：{1}".Fmt(sourceBill["freceivabletobeconfirmed"], receiptUnConfirmedSum, sourceBill["fbillno"]),
                DebugData = "执行了【保存】操作，更新销售合同【收款待确认】。销售合同编号：{2}。原值：{0}。新值：{1}".Fmt(sourceBill["freceivabletobeconfirmed"], receiptUnConfirmedSum, sourceBill["fbillno"]),
                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                Level = Enu_LogLevel.Info.ToString(),
                LogType = Enu_LogType.RecordType_03
            });
            // 更新合同【收款待确认】
            sourceBill["freceivabletobeconfirmed"] = receiptUnConfirmedSum;

            // 订单服务
            var orderService = this.Container.GetService<IOrderService>();
            orderService.CalculateUnreceived(this.Context, new[] { sourceBill });
        }

        /// <summary>
        /// 反写采购订单
        /// </summary>
        /// <param name="incomeDisburse">收支记录数据包</param>
        /// <param name="sourceBills">采购订单数据包</param>
        /// <param name="sourceReceiptRefunds">采购订单收退款金额</param>
        private void BackWritePurchaseOrder(
            DynamicObject incomeDisburse,
            DynamicObject[] sourceBills,
            DynamicObjectCollection sourceReceiptRefunds)
        {
            var sourceId = Convert.ToString(incomeDisburse["fsourceid"]);
            var sourceBill = sourceBills?.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(sourceId));
            if (sourceBill == null) return;

            var confirmamount = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.receiptUnConfirmed);
            // 更新采购订单【待确认金额】
            sourceBill["fconfirmamount"] = confirmamount;
            //【待结算金额】= 【成交金额】-【已付金额】-【待确认金额】
            sourceBill["fpayamount"] = Convert.ToDecimal(sourceBill["ffbillamount"]) - Convert.ToDecimal(sourceBill["fsettleamount"]) - confirmamount;
        }
    }
}