using JieNor.AMS.YDJ.MS.API.DTO.Dept;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.AMS.YDJ.MS.API.Validation;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.Dept
{
    /// <summary>
    /// 部门：同步接口
    /// </summary>
    public class ZYDeptSyncController : BaseController<ZYDeptSyncDto>
    {
        protected HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "ydj_dept"; }
        }

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "number";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(ZYDeptSyncDto dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var resp = new BaseResponse<MuSiData>();

            if (!Valid(dto, resp)) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";
            resp.Data.Flag = MuSiFlag.SUCCESS.ToString();
            var deptDatas = dto.Data;

            #region 查询相关数据
            // 根据外部Id查询所有的经销商Ids
            //string sql = $@"select fid,fnumber,ftranid,fstatus,fagentstatus from t_bas_agent with(nolock) where fnumber in ({dto.Data.Select(x => x.agentno)?.JoinEx(",", true)})";
            //var agents = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            List<ZYDeptData> removeItem = new List<ZYDeptData>();
            string existSql = $@"select fsapcostcenterno,b.fnumber from t_bd_department a with(nolock)  inner join t_bas_agent b with(nolock)  on a.fmainorgid=b.fid where fsapcostcenterno in ({dto.Data.Select(x => x.number)?.JoinEx(",", true)})";
            var deptObjs = this.Context.ExecuteDynamicObject(existSql, new List<SqlParam>());
            foreach (var item in dto.Data)
            {
                var deptObjItem = deptObjs.Where(x => Convert.ToString(x["fsapcostcenterno"]).Equals(item.number)).FirstOrDefault();
                if (deptObjItem == null)
                {
                    if (!string.IsNullOrWhiteSpace(item.freezeFlag) && item.freezeFlag.ToUpper().Equals("X"))
                    {
                        resp.Data.FailedNumbers.Add(item.number);
                        resp.Data.ErrorMsgs.Add($"当前部门{item.number}不存在，请先下发部门！");
                        removeItem.Add(item);
                    }
                }
            }
            deptDatas = deptDatas.Except(removeItem).ToList();
            #endregion
            foreach (var group in deptDatas.GroupBy(s => new { agentid = s.agentno }))
            {
                try
                {
                    //var agentItem = agents.Where(a => Convert.ToString(a["fnumber"]).Equals(group.Key)).FirstOrDefault();
                    //判断经销商和操作人
                    var agentCtx = this.Context.CreateAgentDBContextByNo(group.Key.agentid);
                    if (agentCtx == null)
                    {
                        resp.Data.FailedNumbers.AddRange(group.Select(x => x.agentno));
                        resp.Data.ErrorMsgs.Add($"当前经销商{group.Key.agentid}不存在，请检查数据！");
                        continue;
                    }
                    var datas = this.ConvertToDynsData(agentCtx, group.ToList());

                    // 本批处理的转换数据
                    if (datas != null && datas.Count > 0)
                    {
                        var tranids = datas.Select(x => Convert.ToString(x["fsapcostcenterno"]));
                        var saveDatas = datas.Where(a => !Convert.ToBoolean(a["fforbidstatus"])).ToList();
                        var forbidDatas = datas.Where(a => Convert.ToBoolean(a["fforbidstatus"])).ToList();

                        if (saveDatas.Count > 0)
                        {
                            var prepareSaveDataService = agentCtx.Container.GetService<IPrepareSaveDataService>();
                            prepareSaveDataService.PrepareDataEntity(agentCtx, this.HtmlForm, saveDatas.ToArray(), OperateOption.Create());

                            var result = this.HttpGateway.InvokeBillOperation(agentCtx, this.HtmlForm.Id, saveDatas, "draft", new Dictionary<string, object>());
                            if (result.IsSuccess)
                            {
                                resp.Data.SucceedNumbers.AddRange(tranids);
                            }
                            else
                            {
                                resp.Success = false;
                                resp.Data.FailedNumbers.AddRange(tranids);
                                resp.Data.ErrorMsgs.AddRange(result.ToString()?.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
                            }
                        }
                        if (forbidDatas.Count > 0)
                        {
                            forbidDatas.ForEach(a => a["fforbidstatus"] = "0");
                            var prepareSaveDataService = agentCtx.Container.GetService<IPrepareSaveDataService>();
                            prepareSaveDataService.PrepareDataEntity(agentCtx, this.HtmlForm, forbidDatas.ToArray(), OperateOption.Create());

                            var result = this.HttpGateway.InvokeBillOperation(agentCtx, this.HtmlForm.Id, forbidDatas, "Forbid", new Dictionary<string, object>());
                            if (result.IsSuccess)
                            {
                                resp.Data.SucceedNumbers.AddRange(tranids);
                            }
                            else
                            {
                                resp.Success = false;
                                resp.Data.FailedNumbers.AddRange(tranids);
                                resp.Data.ErrorMsgs.AddRange(result.ToString()?.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
                            }
                        }

                    }
                }
                catch (Exception e)
                {
                    this.LogService.Error(e);
                    resp.Success = false;
                    resp.Data.ErrorMsgs.Add(e.Message);
                }
            }

            if (!resp.Success)
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", resp.Data.ErrorMsgs);
                resp.Data.Flag = resp.Data.SucceedNumbers.Any()
                    ? MuSiFlag.PARTSUCCESS.ToString()
                    : MuSiFlag.FAIL.ToString();
            }
            foreach (var item in resp.Data.SucceedNumbers)
            {
                this.Request.SetBillNo(MSKey.SuccessNumber, item);
            }
            foreach (var item in resp.Data.FailedNumbers)
            {
                this.Request.SetBillNo(MSKey.FailNumber, item);
            }
            return resp;
        }

        //构造数据
        private List<DynamicObject> ConvertToDynsData(UserContext agentCtx, List<ZYDeptData> Datas)
        {
            var dataMeta = this.MetaModelService.LoadFormModel(agentCtx, this.HtmlForm.Id);
            var dataMetaType = dataMeta.GetDynamicObjectType(agentCtx);

            //查询已经存在的数据
            var Ids = Datas.Where(x => !x.number.IsNullOrEmptyOrWhiteSpace()).Select(x => x.number);
            var exsitDatas = agentCtx.LoadBizDataByFilter(this.HtmlForm.Id, $" fsapcostcenterno in ({Ids.JoinEx(",", true)})");

            var dataObjs = new List<DynamicObject>();
            foreach (var data in Datas)
            {
                var dynObj = exsitDatas.Where(x => Convert.ToString(x["fsapcostcenterno"]) == data.number).FirstOrDefault();
                if (dynObj == null)
                {
                    dynObj = dataMetaType.CreateInstance() as DynamicObject;
                    //dynObj["ftranid"] = data.id;
                    dynObj["fsapcostcenterno"] = data.number;
                }
                dynObj["fname"] = data.name;
                if (!string.IsNullOrWhiteSpace(data.freezeFlag))
                {
                    dynObj["fforbidstatus"] = Convert.ToString(data.freezeFlag).ToUpper().Equals("X") ? "1" : "0";
                }
                dataObjs.Add(dynObj);
            }
            return dataObjs;
        }

        private bool Valid(ZYDeptSyncDto dto, BaseResponse<MuSiData> resp)
        {
            if (dto.Data == null || dto.Data.Count == 0)
            {
                resp.Code = 400;
                resp.Message = "参数data不能为空！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);

                return false;
            }

            List<string> agentIds = new List<string>();
            List<string> errorMsgs = new List<string>();

            foreach (var data in dto.Data)
            {
                if (data.number.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数number不能为空！");
                }
                if (data.name.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数name不能为空！");
                }
                else if (data.agentno.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数agentno不能为空！");
                }
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;

                resp.Data.ErrorMsgs = errorMsgs;
                resp.Data.Flag = MuSiFlag.FAIL.ToString();

                return false;
            }

            return true;
        }
    }
}