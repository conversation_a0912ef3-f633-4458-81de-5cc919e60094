///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/coo/coo_inpourdialog.js
*/
; (function () {
    var coo_inpourdialog = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };

        //继承 BasePlugIn
        __extends(_child, _super);

        //联合开单信息
        _child.prototype.dutyEntryId = 'fdutyentry';

        //销售部门控件是否可用
        _child.prototype.deptEnable = true;

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;

            var cp = that.formContext.cp;
            if (!cp) return;
            
            //加载对方银行下拉框数据源
            that.Model.invokeFormOperation({
                id: 'getsynbanknum',
                opcode: 'getsynbanknum',
                opname: '获取协同方银行账号',
                param: {
                    formId: 'coo_inpourdialog',
                    domainType: Consts.domainType.dynamic,
                    pkid: cp.pkid,
                    sourceFormId: cp.formId
                }
            });

            //返利账户时，支付方式默认为空
            if ($.trim(cp.fusagetype.id) === 'settleaccount_type_02') {
                if (!Consts.isdirectsale){
                    that.Model.setValue({ id: 'fway', value: '' });
                }
                else{
                    that.Model.setValue({ id: 'frefundway', value: '' });
                }
            }

            that.HidePaymentdesc();
            that.renewalHeadquart();

            //获取门店系统参数本地缓存数据
            var param = JSON.parse(localStorage.getItem("storesysparam"));
            if (!param) {
                //本地缓存不存在则请求
                that.Model.invokeFormOperation({
                    id: 'loadstoresysparam',
                    opcode: 'loadstoresysparam',
                    param: {
                        'formId': 'bas_storesysparam',
                        'domainType': 'parameter'
                    }
                });
            }
            debugger;
            //默认锁定支付方式--线下支付
            if ($.trim(that.Model.uiData.fway.id) === 'payway_04' && !Consts.isdirectsale) {
                that.Model.setEnable({ id: 'fmybankid', value: false });
                that.Model.setEnable({ id: 'fsynbankid', value: false });
            }
            if ($.trim(that.Model.uiData.frefundway.id) === 'payway_04' && Consts.isdirectsale){
                that.Model.setEnable({ id: 'fmybankid', value: true });
                that.Model.setEnable({ id: 'fsynbankid', value: true });
            }

            //客户唯一性报备启用【来源门店】时，设置【销售部门】并不可用
            if (cp && cp.validStore && cp.validStore.mustFlag) {
                that.Model.setValue({ id: 'fdeptid', value: cp.validStore.storeId });
                that.Model.setEnable({ id: 'fdeptid', value: false });
                that.deptEnable = false;
            }

            that.DealDutyEntry(true);

            $(".receiptno").bind("keyup", function (e) {
                //that.Model.setValue({ id: "freceiptno", value: this.value.replace(/[^0-9A-Za-z]+$/, '') });
                //平台方法会触发值改变事件，放弃使用
                $(".receiptno").val(this.value.replace(/[^0-9A-Za-z]+/g, ''));
            });
            that.hideOrShowDutyEntryField(that.dutyEntryId, 'fdeptperfratio');
        };

        //表单字段标签点击后触发
        _child.prototype.onFieldLabelClick = function (e) {
            switch (e.id.toLowerCase()) {
                case 'fcontactunitid':
                    //辅助资料没有单独的视图文件，不允许点击标签打开，所以这里取消平台标准通用逻辑
                    e.cancel = true;
                    break;
            }
        };

        _child.prototype.HidePaymentdesc = function () {
            var paynum = $("select[name='paymentdesc']").find('option').length;
            if (paynum < 1) {
                $(".hidepaycs").hide();
                $("select[name='paymentdesc']").attr('required', false)
            }

        }

        //总部相关字段 当【登录账号所属经销商.经营类型=直营】默认可见，反之不显示
        _child.prototype.renewalHeadquart = function () {
            debugger
            var that = this;
            var enable = true;
            //直营
            if (Consts.isdirectsale) {
                enable = true;
            } else {
                enable = false;
            }
            that.Model.setVisible({id: '.renewal-info-head', value: !enable});
            that.Model.setVisible({id: '.renewal-info-head-refund', value: enable});
        }

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fmoney':
                    var moneys = $.trim(e.value).split('.');
                    if (moneys && moneys.length === 2 && moneys[1].length > 2) {
                        e.value = yiMath.toNumber(yiMath.toDecimal(e.value, 2));
                        e.result = true;
                        yiDialog.mt({ msg: '收款金额只能输入两位小数点！', skinseq: 2 });
                    }
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'getsynbanknum':
                    if (srvData && srvData.isSyn) {
                        that.synBankNum = srvData.synBankNum;
                        if (that.synBankNum && that.synBankNum.length > 0) {
                            var bankComboData = [{ id: '', name: '&nbsp;' }];
                            for (var i = 0; i < that.synBankNum.length; i++) {
                                bankComboData.push({
                                    id: that.synBankNum[i].accountId,
                                    name: '{0} - {1} - {2}'.format(that.synBankNum[i].bankNum, that.synBankNum[i].accountName, that.synBankNum[i].bankName)
                                });
                            }
                            that.Model.setComboData({ id: 'fsynbankid', data: bankComboData });
                        }
                        that.Model.setVisible({ id: '.syn-banknum', value: true });
                        that.Model.setValue({ id: 'fissyn', value: srvData.isSyn });
                    }
                    break;
                //立即收款
                case 'recharge':
                    if (isSuccess) {
                        var refreshParent = false;
                        if (srvData && srvData.refreshParent) {
                            refreshParent = srvData.refreshParent;
                        }
                        //设置对话框的返回数据
                        that.Model.setReturnData({ isSuccess: true, refreshParent: refreshParent });
                        //关闭对话框
                        that.Model.close();
                    }
                    break;
                case 'loadstoresysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var storesysparam = {};
                        for (var data in srvData) {
                            storesysparam[data] = srvData[data];
                        }
                        localStorage.setItem("storesysparam", JSON.stringify(storesysparam));
                    }
                    break;
                case 'checkinvoicenumber':
                    if (isSuccess && srvData) {
                        that.confirming = false;
                        yiDialog.c("【收款小票号】录入重复，您确定继续吗？", function () {
                            if (that.confirming) return;
                            that.confirming = true;

                            that.checkInvoiceNumberPass();
                        }, null, '温馨提示');
                    }
                    else {
                        that.checkInvoiceNumberPass();
                    }
                    break;
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //取消
                case 'costcancel':
                    e.result = true;
                    that.Model.close();
                    break;
                //立即收款
                case 'costconfirm':
                    e.result = true;
                    var staffOk = that.checkStaff();
                    var deptPerfRatioOk = that.checkStaffRatioIsOneHundredPercent('fdeptperfratio');
                    if (staffOk && deptPerfRatioOk) {
                        var cloneData = that.Model.clone();
                        var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
                        var enableMustInputBankId = storeParam ? storeParam.fenablemustinputbankid : true;

                        //处理对方银行账号信息
                        var frefundway = $.trim(cloneData.frefundway.id);
                        var way = $.trim(cloneData.fway.id);
                        if (Consts.isdirectsale){
                            way = frefundway;
                        }
                        //非直营校验支付方式
                        if (!way && !Consts.isdirectsale) {
                            yiDialog.warn('支付方式不能为空！');
                            return;
                        }
                        //直营校验付款方式
                        if (!frefundway && Consts.isdirectsale) {
                            yiDialog.warn('付款方式不能为空！');
                            return;
                        }
                        
                        if (way === 'payway_06' ||
                            way === 'payway_07' ||
                            way === 'payway_08' ||
                            (way === 'payway_11' && enableMustInputBankId)) {
                            if (!$.trim(cloneData.fmybankid.id)) {
                                yiDialog.warn('请选择银行账号！');
                                return;
                            }
                            if (cloneData.fissyn) {
                                var synBankId = $.trim(cloneData.fsynbankid.id);
                                if (!synBankId) {
                                    yiDialog.warn('请选择对方银行！');
                                    return;
                                }
                                var banks = that.synBankNum;
                                if (banks && banks.length > 0) {
                                    for (var i = 0; i < banks.length; i++) {
                                        if ($.trim(banks[i].accountId) === synBankId) {
                                            cloneData.fsynbankname = banks[i].bankName;
                                            cloneData.fsynbanknum = banks[i].bankNum;
                                            cloneData.fsynaccountname = banks[i].accountName;
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        var money = yiMath.toNumber(cloneData.fmoney);
                        if (money < 0.01) {
                            yiDialog.mt({ msg: '收款金额必须大于0！', skinseq: 2 });
                            return;
                        }
                        var usageType = $.trim(cloneData.fusagetype.id);
                        if (!usageType) {
                            yiDialog.mt({ msg: '请选择要收款的账户！', skinseq: 2 });
                            return;
                        }
                        var deptid = $.trim(cloneData.fdeptid.id);
                        if (!deptid) {
                            yiDialog.mt({ msg: '销售部门不能为空！', skinseq: 2 });
                            return;
                        }
                        var staffid = $.trim(cloneData.fstaffid.id);
                        if (!staffid) {
                            yiDialog.mt({ msg: '销售员不能为空！', skinseq: 2 });
                            return;
                        }
                        var paymentdesc = $.trim(cloneData.paymentdesc.id);
                        var paynum = $("select[name='paymentdesc']").find('option').length;
                        if (!paymentdesc && paynum > 0) {
                            yiDialog.warn('款项说明不能为空！');
                            return;
                        }

                        var receiptno = that.Model.getValue({ id: 'freceiptno' });
                        if (receiptno) {
                            var money = that.Model.getValue({ id: 'fmoney' });
                            that.Model.invokeFormOperation({
                                id: 'checkinvoicenumber',
                                opcode: 'checkinvoicenumber',
                                param: {
                                    'formId': 'coo_incomedisburse',
                                    'freceiptno': receiptno,
                                    'fmoney': money
                                }
                            });
                        }
                        else {
                            that.Model.invokeFormOperation({
                                id: 'Recharge',
                                opcode: 'Recharge',
                                billData: [cloneData],
                                param: {
                                    fsourceid: that.formContext.cp.pkid,
                                    fsourceformid: that.formContext.cp.formId
                                }
                            });
                        }
                    }
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            switch (e.id) {
                case "fway":
                case "frefundway":
                    if (e.value.id != "payway_06" &&
                        e.value.id != "payway_07" &&
                        e.value.id != "payway_08" &&
                        e.value.id != "payway_11") {
                        that.Model.setValue({ id: "fmybankid", value: "" });
                        that.Model.setValue({ id: "fsynbankid", value: "" });
                    }
                    debugger;
                    var wayDatas = that.Model.viewModel.uiComboData.fway;
                    if (Consts.isdirectsale){
                        wayDatas= that.Model.viewModel.uiComboData.frefundway;
                    }
                    var payData = wayDatas.find(function (x) { return e.value.id == x.id });

                    var fwayitems = ["payway_06", "payway_11", "payway_07", "payway_08"];
                    var hasval = $.inArray(e.value.id, fwayitems);
                    //处理任务：30856 子 【慕思现场】客户充值界面，选择 嘉联平台收款 有赞平台收款 这两个结算方式，银行账号可编辑 / 客户新增的支付方式，需要设置银行账号必填 - PC端
                    if (hasval != -1 || (hasval == -1 && payData && !payData.isPrepare)) {
                        that.Model.setEnable({ id: 'fmybankid', value: true });
                        that.Model.setEnable({ id: 'fsynbankid', value: true });
                    } else {
                        that.Model.setEnable({ id: 'fmybankid', value: false });
                        that.Model.setEnable({ id: 'fsynbankid', value: false });
                    }
                    break;
                case 'fstaffid':
                    debugger;
                    if (that.deptEnable) {
                        //40936 客户充值默认携带业务员，允许业务员修改-后端开发
                        var fdeptid = that.GetDeptByStaff(e.value.id);
                        if (fdeptid && fdeptid != "") {
                            that.Model.setValue({ id: 'fdeptid', value: fdeptid });
                        }
                    }
                    that.DealDutyEntry(false, e);
                    break;
                case 'fdeptid':
                    that.DealDutyEntry(false, e);
                    break;
                case 'fmoney':
                    that.calculateStaff();
                //that.checkInvoiceNumber();
                case 'fratio':
                case 'famount_ed':
                    that.calculateStaff(e);
                    break;
                case 'fdutyid':
                    //将销售员部门自动填充到人员明细表格中
                    var fdeptid = that.GetDeptByStaff(e.value.id);
                    if (fdeptid && fdeptid != "") {
                        that.Model.setValue({ id: 'fdeptid_ed', value: fdeptid, row: e.row, tgChange: false });
                    }
                    break;
                case 'freceiptno':
                    debugger;
                    //that.checkInvoiceNumber();
                    break;
                case 'fdeptperfratio':
                    that.calculateStaffAmountAndRatio(e,null,'fdeptperfratio');
                    break;
            }
        };

        //根据员工带出其主岗位对应的部门(通过mdl配置带出的部门不正确，需要是员工明细勾选的主岗位)
        _child.prototype.GetDeptByStaff = function (fid) {
            var that = this;
            var fdeptid = "";
            var param = {
                simpleData: {
                    formId: 'coo_incomedisburse',
                    fid: fid,
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/coo_incomedisburse?operationno=getdeptbyfid', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (!res.isSuccess) {
                    fdeptid = "";
                } else {
                    fdeptid = srvData["fdeptid"];
                }
            }, null, null, null, { async: false });
            return fdeptid;
        };

        //联合开单数据处理主岗位数据
        _child.prototype.DealDutyEntry = function (isadd, e) {
            var that = this;
            var dutyEntry = that.Model.getEntryData({ id: that.dutyEntryId });
            if (isadd) {
                var dept = that.Model.getValue({ id: 'fdeptid' });
                // 固定首行数据
                that.Model.addRow({
                    id: that.dutyEntryId, data: {
                        fismain: true,
                        fdutyid: that.Model.getValue({ id: 'fstaffid' }),
                        fdeptid_ed: { "id": dept.id, "fnumber": dept.fnumber, "fname": dept.fname },
                        fratio: 100,
                        famount_ed: 0,
                        fdeptperfratio:100
                    }
                });
                for (var i = 0; i < dutyEntry.length; i++) {
                    if (dutyEntry[i].fismain) {
                        var row = dutyEntry[i].id;
                        that.Model.setEnable({ id: 'fdutyid', row: row, value: false });
                        that.Model.setEnable({ id: 'fdeptid_ed', row: row, value: false });
                        break;
                    }
                }
            } else {
                //修改首行数据
                for (var i = 0; i < dutyEntry.length; i++) {
                    if (dutyEntry[i].fismain) {
                        var row = dutyEntry[i].id;
                        if (e && e.id == "fstaffid") {
                            that.Model.setValue({ id: 'fdutyid', row: row, value: e.value.id, tgChange: false });
                        }
                        if (e && e.id == "fdeptid") {
                            that.Model.setValue({ id: 'fdeptid_ed', row: row, value: e.value.id });
                        }
                        break;
                    }
                }
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.dutyEntryId:
                    var isMain = that.Model.getValue({ id: 'fismain', row: e.row });
                    if (isMain) {
                        e.result = true;
                        yiDialog.mt({ msg: '主要销售员不允许删除！', skinseq: 2 });
                        return;
                    }
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.dutyEntryId:
                    that.calculateStaff("delete");
                    that.calculateStaffAmountAndRatio("delete",null,'fdeptperfratio');
                    break;
            }
        }

        //计算销售员金额比例行联动
        _child.prototype.calculateStaff = function (e) {
            var that = this,
                data = that.Model.getEntryData({ id: that.dutyEntryId }),
                sumAmount = yiMath.toNumber(that.Model.getValue({ id: 'fmoney' }));
            if (!data || data.length <= 0) { return; }

            //只有一个导购员时，自动设置为订单总额
            if (data.length === 1) {
                data[0].famount_ed = sumAmount;
                data[0].fratio = 100;
                that.Model.refreshEntry({ id: that.dutyEntryId });
                return;
            }
            //有两个导购员时，修改其中一个导购员的金额或比例后，自动计算另外一个导购员的金额和比例
            if (e && data.length === 2) {
                if (e == "delete") {
                    //删除行小于等于2个人的时候需要做计算给到第一个人，即100%减去第2个人给到第1个人
                    for (var i = 0; i < data.length; i++) {
                        if (i == 0) {
                            data[i].fratio = 100 - data[1].fratio;
                        }
                        data[i].famount_ed = yiMath.toDecimal(sumAmount * data[i].fratio / 100, 2);
                    }
                } else {
                    for (var i = 0; i < data.length; i++) {
                        //金额字段可编辑，自动反算比例
                        if (e.id == "famount_ed") {
                            if (sumAmount <= 0) continue;
                            e.value = e.value > sumAmount || e.value < 0 ? sumAmount : e.value;
                            if (data[i].id === e.row) {
                                data[i].famount_ed = e.value;
                            } else {
                                data[i].famount_ed = sumAmount - e.value;
                            }
                            data[i].fratio = yiMath.toDecimal(data[i].famount_ed / sumAmount * 100, 2);
                        }
                        //比例字段可编辑，并可自动反算金额
                        if (e.id == "fratio") {
                            e.value = e.value > 100 || e.value < 0 ? 100 : e.value;
                            if (data[i].id === e.row) {
                                data[i].fratio = e.value;
                            } else {
                                data[i].fratio = 100 - e.value;
                            }
                            data[i].famount_ed = yiMath.toDecimal(sumAmount * data[i].fratio / 100, 2);
                        }
                    }
                }
                that.Model.refreshEntry({ id: that.dutyEntryId });
                return;
            }

            var useAmt = sumAmount;
            var useRatio = 100.00;
            //单行金额或者比例计算
            for (var i = 0, l = data.length; i < l; i++) {
                //金额字段可编辑，自动反算比例
                if (e && e.id == "famount_ed") {
                    if (sumAmount <= 0) continue;
                    //行所占百分比 = 金额/订单总额 * 100
                    data[i].famount_ed = data[i].famount_ed > sumAmount || data[i].famount_ed < 0 ? 0 : data[i].famount_ed;
                    data[i].fratio = yiMath.toDecimal(data[i].famount_ed / sumAmount * 100, 2);
                } else {
                    var ratio = yiMath.toNumber(data[i].fratio);
                    //金额 = 订单总额 * 行所占百分比
                    data[i].fratio = ratio > 100 || ratio < 0 ? 0 : data[i].fratio;
                    data[i].famount_ed = yiMath.toDecimal(sumAmount * data[i].fratio / 100, 2);
                }
                useAmt -= yiMath.toDecimal(data[i].famount_ed, 2);
                useRatio -= yiMath.toDecimal(data[i].fratio, 2);
            }

            //处理比例/金额尾差分配给主销售员
            var mainrow = data.findIndex(item => item.fismain === true);
            if (useAmt > 0) data[mainrow].famount_ed = yiMath.toDecimal(yiMath.toNumber(data[mainrow].famount_ed) + yiMath.toNumber(useAmt), 2);
            if (useRatio > 0) data[mainrow].fratio = yiMath.toDecimal(yiMath.toNumber(data[mainrow].fratio) + yiMath.toNumber(useRatio), 2);

            that.Model.refreshEntry({ id: that.dutyEntryId });
        };

        //检查销售员比例和金额是否填写正确
        _child.prototype.checkStaff = function () {
            var that = this;
            var ratioSum = 0;
            var amountSum = 0;
            var data = that.Model.getEntryData({ id: that.dutyEntryId });
            if (data && data.length > 0) {
                for (var i = 0, l = data.length; i < l; i++) {
                    ratioSum = yiMath.toNumber(yiMath.toDecimal(ratioSum, 2));
                    ratioSum += yiMath.toNumber(data[i].fratio);

                    amountSum += yiMath.toNumber(yiMath.toDecimal(data[i].famount_ed, 2));
                }
            }
            if (ratioSum != 100) {
                yiDialog.warn('销售员分配比例总和必须等于100%');
                return false;
            }
            var sumAmount = yiMath.toNumber(that.Model.getValue({ id: 'fmoney' }));
            if (amountSum != sumAmount) {
                yiDialog.warn('销售员分配金额总和必须等于收款金额！');
                return false;
            }
            return true;
        };

        //检查收款小票号跟金额
        _child.prototype.checkInvoiceNumber = function () {
            var that = this;
            var receiptno = that.Model.getValue({ id: 'freceiptno' });
            if (receiptno) {
                var money = that.Model.getValue({ id: 'fmoney' });
                that.Model.invokeFormOperation({
                    id: 'checkinvoicenumber',
                    opcode: 'checkinvoicenumber',
                    param: {
                        'formId': 'coo_incomedisburse',
                        'freceiptno': receiptno,
                        'fmoney': money
                    }
                });
            }
        };

        //检查收款小票号跟金额通过
        _child.prototype.checkInvoiceNumberPass = function () {
            var that = this;

            var cloneData = that.Model.clone();
            that.Model.invokeFormOperation({
                id: 'Recharge',
                opcode: 'Recharge',
                billData: [cloneData],
                param: {
                    fsourceid: that.formContext.cp.pkid,
                    fsourceformid: that.formContext.cp.formId
                }
            });
        };
        _child.prototype.calculateStaffAmountAndRatio = function (e, amount, ratio){
            var that = this;
            data = that.Model.getEntryData({ id: that.dutyEntryId });
            sumAmount = yiMath.toNumber(that.Model.getValue({ id: 'fmoney' }));
            if (!data || data.length <= 0) { return; }

            //只有一个导购员时，自动设置为订单总额
            if (data.length === 1) {
                //data[0][amount] = sumAmount;
                data[0][ratio] = 100;

                that.Model.refreshEntry({ id: that.dutyEntryId });
                return;
            }
            //有两个导购员时，修改其中一个导购员的金额或比例后，自动计算另外一个导购员的金额和比例
            if (e && data.length === 2) {
                if (e == "delete") {
                    //删除行小于等于2个人的时候需要做计算给到第一个人，即100%减去第2个人给到第1个人
                    for (var i = 0; i < data.length; i++) {
                        if (i == 0) {
                            data[i][ratio] = 100 - data[1][ratio];
                        }
                        //data[i][amount] = yiMath.toDecimal(sumAmount * data[i][ratio] / 100, 2);
                    }
                } else {
                    for (var i = 0; i < data.length; i++) {
                        //金额字段可编辑，自动反算比例
                        if (e.id === amount) {
                            if (sumAmount <= 0) continue;
                            e.value = e.value > sumAmount || e.value < 0 ? sumAmount : e.value;
                            if (data[i].id === e.row) {
                                data[i][amount] = e.value;
                            } else {
                                data[i][amount] = sumAmount - e.value;
                            }
                            data[i][ratio] = yiMath.toDecimal(data[i][amount] / sumAmount * 100, 2);
                        }
                        //比例字段可编辑，并可自动反算金额
                        if (e.id === ratio) {
                            e.value = e.value > 100 || e.value < 0 ? 100 : e.value;
                            if (data[i].id === e.row) {
                                data[i][ratio] = e.value;
                            } else {
                                data[i][ratio] = 100 - e.value;
                            }
                            //data[i][amount] = yiMath.toDecimal(sumAmount * data[i][ratio] / 100, 2);
                        }
                    }
                }

                that.Model.refreshEntry({ id: that.dutyEntryId });
                return;
            }

            var useAmt = sumAmount;
            var useRatio = 100.00;
            //单行金额或者比例计算
            for (var i = 0, l = data.length; i < l; i++) {
                //金额字段可编辑，自动反算比例
                if (e && e.id === amount) {
                    if (sumAmount <= 0) continue;
                    //行所占百分比 = 金额/订单总额 * 100
                    //data[i][amount] = data[i][amount] > sumAmount || data[i][amount] < 0 ? 0 : data[i][amount];
                    data[i][ratio] = yiMath.toDecimal(data[i][amount] / sumAmount * 100, 2);
                } else {
                    var ratio = yiMath.toNumber(data[i].fratio);
                    //金额 = 订单总额 * 行所占百分比
                    data[i][ratio] = ratio > 100 || ratio < 0 ? 0 : data[i][ratio];
                    //data[i][amount] = yiMath.toDecimal(sumAmount * data[i][ratio] / 100, 2);
                }
                //useAmt -= yiMath.toDecimal(data[i][amount], 2);
                useRatio -= yiMath.toDecimal(data[i][ratio], 2);
            }

            //处理比例/金额尾差分配给主销售员
            var mainrow = data.findIndex(item => item.fismain === true);
            //if (useAmt > 0) data[mainrow][amount] = yiMath.toDecimal(yiMath.toNumber(data[mainrow][amount]) + yiMath.toNumber(useAmt), 2);
            if (useRatio > 0) data[mainrow][ratio] = yiMath.toDecimal(yiMath.toNumber(data[mainrow][ratio]) + yiMath.toNumber(useRatio), 2);

            that.Model.refreshEntry({ id: that.dutyEntryId });
        }
        //检查销售员比例和金额是否填写正确
        _child.prototype.checkStaffRatioIsOneHundredPercent = function (ratio) {
            var that = this;
            var ratioSum = 0;
            //直营模式下面才进行下面的判断
            if(!Consts.isdirectsale){
                return true;
            }
            var data = that.Model.getEntryData({ id: that.dutyEntryId });
            if (data && data.length > 0) {
                for (var i = 0, l = data.length; i < l; i++) {
                    ratioSum = yiMath.toNumber(yiMath.toDecimal(ratioSum, 2));
                    ratioSum += yiMath.toNumber(data[i][ratio]);

                }
            }
            if (ratioSum != 100) {
                var errormsg = '';
                switch (ratio.toLocaleLowerCase()) {
                    //部门业绩比例
                    case 'fdeptperfratio':
                        errormsg = '部门业绩比例总和必须等于100%！';
                        break;
                }
                yiDialog.warn(errormsg);
                return false;
            }
            return true;
        };
        //直营的时候需要把联合开单的比例显示，经销商隐藏隐藏
        _child.prototype.hideOrShowDutyEntryField = function (dutyEntryId,fieldIdArray){
            var that = this;
            var dutyEntry = that.Model.getEntryData({id:dutyEntryId});
            var fieldIdList = fieldIdArray.split(',');
            if(Consts.isdirectsale){
                if(dutyEntry && dutyEntry.length > 0){
                    for (var i = 0; i < dutyEntry.length; i++) {
                        for (var j = 0; j < fieldIdList.length; j++) {
                            that.Model.setVisible({ id: fieldIdList[i], value: true });
                        }
                    }
                }
            }else{
                if(dutyEntry && dutyEntry.length > 0){
                    for (var i = 0; i < dutyEntry.length; i++) {
                        for (var j = 0; j < fieldIdList.length; j++) {
                            that.Model.setVisible({ id: fieldIdList[i], value: false });
                        }
                    }
                }
            }
        }
        return _child;
    })(BasePlugIn);
    window.coo_inpourdialog = window.coo_inpourdialog || coo_inpourdialog;
})();