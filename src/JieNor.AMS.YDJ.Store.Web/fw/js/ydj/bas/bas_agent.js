(function () {
    var bas_agent = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.flag = true;
        };
        __extends(_child, _super);
        _child.prototype.brandEntryId = 'fratioinfo';
        
        //初始化编辑页面插件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                that.isFirstLevelAgent();
            }
            //if (that.Model.getValue({ id: "fstatus" })&&that.Model.getValue({ id: "fstatus" }).id != 'C') {
                that.Model.setEnable({ id: "foldagentid", value: false });
            //}

            that.Model.setVisible({ id: '.reminddate', value: that.Model.getValue({ id: "fismember" }) });
            that.setFieldMustFlag({ id: "freminddate", caption: "会员注册提醒启用日期", must: that.Model.getValue({ id: "fismember" }) });
            that.setFieldMustFlag({ id: "fpricerule", caption: "二级分销合同价格计算规则", must: !Consts.istoporg && !Consts.isSecondOrg });
            var managemodel = that.Model.getSimpleValue({ id: "fmanagemodel" });
            if (managemodel == 1) {
                that.Model.setEnable({ id: "ftoppiecesendtag", value: true });
                that.Model.setVisible({ id: '.agentmodel', value: true });
            } else {
                that.Model.setEnable({ id: "ftoppiecesendtag", value: false });  
                that.Model.setVisible({ id: '.agentmodel', value: false });
            }  
        };
        _child.prototype.isFirstLevelAgent = function () {
            var that = this;
            yiAjax.p('/bill/bas_agent?operationno=isFirstLevelAgent', null, function (r) {
                var res = r.operationResult;
                that.Model.setValue({ id: "fisreseller", value: res.srvData });
            }, null, null, null, { async: false });
        }

        _child.prototype.onBillInitialized = function (e) {
            var that = this;
            that.hideDirectSalesGiveAwayNotZero(e);
        }

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case "save":
                    that.ValidBrandInfo(e);
                    return;
                //导购员信息
                case 'changelicense':
                    e.result = true;
                    that.Model.showPopForm({ popup: 'license-info' });
                    that.Model.setEnable({ id: "foldagentid", value: true });
                    //每次弹出营业执照更换后清空营业执照
                    that.Model.setEnable({ id: "fnewagentid", value: true });
                    that.Model.setValue({ id: "fnewagentid", value: "" });
                    break;
                case 'licenseconfirm':
                    e.result = true;
                    var staffOk = that.changeLicense();
                    that.Model.setEnable({ id: "foldagentid", value: false });
                    //if (that.Model.getValue({ id: "fstatus" }).id == 'C') {
                    //    that.Model.setEnable({ id: "fname", value: true });
                    //}
                    //that.Model.setValue({ id: "fnumber", value: that.Model.getValue({ id: "fnewnumber" }) });
                    //that.Model.setValue({ id: "fname", value: that.Model.getValue({ id: "fnewname" }) });

                    break;
                case 'licensecancel':
                    e.result = true;
                    that.Model.refresh();
                    that.Model.hidePopForm({ popup: 'license-info' });
                    that.Model.setEnable({ id: "foldagentid", value: false });

                    break;
                case 'forbid':
                    e.result = true;
                    var selectedRows;
                    if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                        selectedRows = [{ PKValue: that.Model.pkid }];

                        var isReseller = that.Model.getSimpleValue({ id: "fisreseller" });
                        if (!isReseller) {
                            e.result = false;
                            return;
                        }
                    }
                    if (that.Model.viewModel.domainType == Consts.domainType.list)
                    {
                        selectedRows = that.Model.getSelectRows();
                        if (!selectedRows || selectedRows.length <= 0) {
                            yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                            return;
                        }
                        //如果是一级经销商禁用则不提示
                        if (selectedRows.some(o => o.data.fisreseller == "0"))
                        {
                            e.result = false;
                            return;
                        }
                    }
                    yiDialog.c('禁用此二级分销商后，该组织下的所有用户及员工均会被禁用，后续反禁用也只会将实控人用户反禁用，请确认是否禁用？', function () {
                        that.Model.invokeFormOperation({
                            id: 'forbid',
                            opcode: 'forbid',
                            selectedRows: selectedRows
                        });
                    });
                    break;
            }
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'foldagentid':
                    e.result.filterString = "  fid<>'" + that.Model.getValue({ id: "id" }) + "' ";
                    break;
                case 'fnewagentid':
                    e.result.filterString = "  fid<>'" + that.Model.getValue({ id: "id" }) + "' and fstatus = 'E' and actualownernumber = '" + that.Model.getValue({ id: "actualownernumber" }) + "' ";
                    break;
            }
        };

        //
        _child.prototype.ValidBrandInfo = function (e) {
            var that = this;
            var isReseller = that.Model.getSimpleValue({ id: "fisreseller" });
            if (!isReseller) {
                //前端
                e.result = true;
                //从前端传参数给后端，后端的接收到参数之后，用来判断
                that.Model.invokeFormOperation({
                    id: 'tbSave',
                    opcode: 'save',
                    param: {
                        formId: 'bas_agent',
                        fromFrontEnd:'__fromFrontEnd__'
                    }
                });
                return;
            }
            var brandEntryData = that.Model.getEntryData({ id: that.brandEntryId });

            if (brandEntryData === null || brandEntryData.length <= 0) {
                return;
            } 
            var groupData = [];
            for (var i = 0; i < brandEntryData.length; i++) {
                if (brandEntryData[i].fseriesid.id == '') {
                    continue;
                }
                if (brandEntryData[i].fresellratio_e<=0) {
                    yiDialog.mt({ msg: "【分销价格系数明细配置】表体中第【" + parseInt(i + 1) + "】行分销价格系数需大于0，请检查！", skinseq: 3 });
                    e.result = true;
                    break;
                }
                var key = brandEntryData[i].fseriesid.fname;
                //if (brandEntryData[i].fcategoryid.id != '') {
                //    key += "-" + brandEntryData[i].fcategoryid.fname;
                //}
                if (groupData.indexOf(key)>-1) {
                    yiDialog.mt({ msg: "【分销价格系数明细配置】表体中【" + key+"】已重复配置，请检查！", skinseq: 3 });
                    e.result = true;
                    break;
                }
                groupData.push(key);
            }
        }

        _child.prototype.changeLicense = function () {
            var that = this;
            var foldagentid = that.Model.getValue({ id: "foldagentid" });
            var fnewagentid = that.Model.getValue({ id: "fnewagentid" });

            if (!fnewagentid || !fnewagentid.id) {
                yiDialog.warn('请选择一个经销商！');
                return false;
            }
            var param= {
                'fnewagentid': fnewagentid.id
                }
            that.Model.invokeFormOperation({
                id: 'tbChangeLicense',
                opcode: 'ChangeLicenseNew',
                param: {
                    'fnewagentid': fnewagentid.id
                }
            });
            //var data = that.Model.getEntryData({ id: that.dutyEntryId });

            //if (data && data.length > 0) {
            //    for (var i = 0, l = data.length; i < l; i++) {
            //        ratioSum += yiMath.toNumber(data[i].fratio);
            //    }
            //}
            ////that.Model.setValue({ id: 'famount_ed', value: 0, row: data[i].id });
            //if (ratioSum != 100) {
            //    yiDialog.warn('销售员分配比例总和必须等于100%');
            //    return false;
            //}

            return true;
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            //switch (e.id.toLowerCase()) {
            //    case 'fcontacterphone': case 'ftaxpayerphone':
            //        var reg = '/^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/';
            //        if (!reg.match(e.value)) {
            //            yiDialog.warn("联系电话格式有误！");
            //        }
            //        break;
            //}
            switch (e.id.toLowerCase()) {
                case 'fismember':
                    that.setFieldMustFlag({ id: "freminddate", caption: "会员注册提醒启用日期", must: e.value });
                    that.Model.setVisible({ id: '.reminddate', value: e.value });
                    break;
                case 'fmanagemodel':
                    if (e.value.id == 1) {
                        that.Model.setEnable({ id: "ftoppiecesendtag", value: true });
                    } else {
                        that.Model.setEnable({ id: "ftoppiecesendtag", value: false });
                    }
                    that.hideDirectSalesGiveAwayNotZero(e);
                    break;
            }
        }

        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            var that = this;
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };



        //操作后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'changelicensenew':
                    var fnewagentid = that.Model.getSimpleValue({ id: "fnewagentid" });
                    //新营业执照切换成功后 更新旧营业执照
                    that.Model.setValue({ id: "foldagentid", value: fnewagentid});
                    that.Model.refresh();
                    if (isSuccess) {
                        that.Model.hidePopForm({ popup: 'license-info' });
                        that.Model.refresh();
                        //that.Model.refresh();
                        //that.Model.invokeFormOperation({
                        //    id: 'tbChangeLicenseAfter',
                        //    opcode: 'ChangeLicenseAfter'
                        //});
                    }
                    
                    break;
            }
        };

        //表格行按钮点击时触发
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == 'fentity') {
                switch (e.btnid.toLowerCase()) {
                    case 'flicenseaddset':
                        e.result = true;
                        that.Showaddressselect(e, "flicenseaddset");
                        break;
                    case 'fauthorizeaddset':
                        e.result = true;
                        that.Showaddressselect(e, "fauthorizeaddset");
                        break;
                }
            }
        };

        //双击确定选择经销商
        _child.prototype.onEntryRowDblClick = function (e) {
            var that = this;
            var page = Index.getPage(that.Model.viewModel.pageId);
            if (e.listMode == "lookup") {
                //关闭对话框
                that.Model.close();
                page.closeCallback({ selectedRows: [{ pkValue: e.data.fbillhead_id }] });
            }
        };

        //选择地址
        _child.prototype.Showaddressselect = function (e, stype) {
            var that = this;
            var fprovince = "";
            var fcity = "";
            var fregion = "";
            if (stype == "flicenseaddset") {
                fprovince = that.Model.getValue({ id: "flicenseprovince", row: e.row });
                fcity = that.Model.getValue({ id: "flicensecity", row: e.row });
                fregion = that.Model.getValue({ id: "flicenseregion", row: e.row });
            }
            else {
                fprovince = that.Model.getValue({ id: "fauthorizeprovince", row: e.row });
                fcity = that.Model.getValue({ id: "fauthorizecity", row: e.row });
                fregion = that.Model.getValue({ id: "fauthorizeregion", row: e.row });
            }
            that.Model.showForm({
                formId: 'bas_addressselect',
                openStyle: 'Modal',
                cp: {
                    fprovince: fprovince,
                    fcity: fcity,
                    fregion: fregion,
                    frow: e.row,
                    callback: function (result) {
                        if (!result || !result.isSuccess) {
                            return;
                        }
                        changePack = true;
                        if (stype == "flicenseaddset") {
                            var flicenseaddress = result.fprovince.fname + result.fcity.fname + result.fregion.fname;
                            that.Model.setValue({ id: "flicenseaddress", row: result.frow, value: flicenseaddress });
                            that.Model.setValue({ id: "flicenseprovince", row: result.frow, value: result.fprovince.id });
                            that.Model.setValue({ id: "flicensecity", row: result.frow, value: result.fcity.id });
                            that.Model.setValue({ id: "flicenseregion", row: result.frow, value: result.fregion.id });
                        }
                        else {
                            var fauthorizeaddress = result.fprovince.fname + result.fcity.fname + result.fregion.fname;
                            that.Model.setValue({ id: "fauthorizeaddress", row: result.frow, value: fauthorizeaddress });
                            that.Model.setValue({ id: "fauthorizeprovince", row: result.frow, value: result.fprovince.id });
                            that.Model.setValue({ id: "fauthorizecity", row: result.frow, value: result.fcity.id });
                            that.Model.setValue({ id: "fauthorizeregion", row: result.frow, value: result.fregion.id });
                        }
                        changePack = false;
                    }
                }
            });
        }

        _child.prototype.hideDirectSalesGiveAwayNotZero = function (e){
            debugger;
            var that = this;
            that.Model.getValue({id:'fdirectsalesgiveawaynotzero'});
            var managemodel = '0';
            if(e.id){
                managemodel = e.value.id;
            }else{
                var managemodelObj  = that.Model.getValue({id:'fmanagemodel'});
                managemodel = managemodelObj.id;
            }
            if(managemodel === '1'){
                that.Model.setVisible({ id: '.hide_fdirectsalesgiveawaynotzero', value: true });
                if(!that.Model.pkid){
                    that.Model.setValue({ id: 'fdirectsalesgiveawaynotzero', value: '1' });
                }
            }else{
                that.Model.setVisible({ id: '.hide_fdirectsalesgiveawaynotzero', value: false });
                if(!that.Model.pkid){
                    that.Model.setValue({ id: 'fdirectsalesgiveawaynotzero', value: '0' });
                }
                that.Model.setEnable({ id: 'fdirectsalesgiveawaynotzero', value: false });
                
            }
        }
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'inventorydimension':
                    var matObj = e.value;
                    var allRows = that.Model.getEntryData({ id: "dimfirstlevelinventory" });
                    if (allRows.some(a => a.inventorydimension.id == matObj.id)) {
                        //var oldVal = that.Model.getValue({ id: 'dimfirstlevelinventory', row: e.row });
                        e.value = e.oldvalue;
                        e.result = true;
                        return;
                    }
                    break;
                case 'fstorehouseid':
                    var matObj = e.value;
                    var allRows = that.Model.getEntryData({ id: "inventory" });
                    if (allRows.some(a => a.fstorehouseid.id == matObj.id)) {
                        e.value = e.oldvalue;
                        e.result = true;
                        return;
                    }
                    break;
            }
        }
        return _child;
    })(BasePlugIn);
    window.bas_agent = window.bas_agent || bas_agent;
})();