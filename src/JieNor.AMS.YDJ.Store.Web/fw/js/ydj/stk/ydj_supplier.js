//库存管理-供应商
; (function () {
    var ydj_supplier = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
        
        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.capitalEntryId = 'fentry';
        _child.prototype.brandEntryId = 'fbrandinfo';
        _child.prototype.supcontacttryId = 'fsupcontacttry';
        _child.prototype.canRechargeAccounts = [];
        
        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.capitalEntryId:
                case this.brandEntryId:
                case this.supcontacttryId:
                    e.result = { rownumbers: false,multiselect:false };
                    break;
            }
            var that = this;
            var pkid = that.Model.pkid;
            yiAjax.p("/dynamic/ydj_supplier?operationno=getRechargeAccounts", { selectedRows: [{ PKValue: pkid }] }, function (r) {
                _child.prototype.canRechargeAccounts = r.operationResult.srvData || [];
            }, null, null, null, { async: false });
        };
        
		//初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
        	var that = this;
			//编辑时处理相关字段的隐藏和显示
			var coocompany = that.Model.getSimpleValue({ id: 'fcoocompany' });
            var coostate = that.Model.getSimpleValue({ id: 'fcoostate' });
            var pkid = that.Model.pkid;
            that.proccooStatusOp(coocompany);
            that.showSynbtn(pkid);
            that.showAccount(coostate);
            that.renewalHeadquart();
        };

        //总部相关字段 当【登录账号所属经销商.经营类型=直营】默认可见，反之不显示
        _child.prototype.renewalHeadquart = function () {
            var that = this;
            var enable = true;
            //直营
            if (Consts.isdirectsale) {
                enable = true;
            }
            else {
                enable = false;
            }
            that.Model.setVisible({ id: '.renewal-info-head', value: enable });
        }
        
        //业务插件内容写在此
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;
            var canRechargeAccounts = _child.prototype.canRechargeAccounts;
            if (that.formContext.domainType == 'bill') {
                var flag = that.Model.getValue({ id: 'id' });
                if (!flag || flag == '') {
                    return e.result = [{
                        id: 'recharge',
                        text: '充值',
                        disabled: true
                    }, {
                        id: 'debit',
                        text: '扣款',
                        disabled: true
                    }];
                }
            }
        };
        
        
        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            switch (e.btnid.toLowerCase()) {
                case 'recharge':
                    that.recharge(e);
                    break;
                case 'debit':
                    that.debit(e);
                    break;
            }
        };

        //字段值改变时
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fisthree':
                    //计算
                    that.Model.setValue({id:'flicenseimage',value:''});
                    that.Model.setValue({id:'forganizationimage',value:''});
                    that.Model.setValue({id:'ftaximage',value:''});
                    break;
            }
        };
        
        //充值
        _child.prototype.recharge = function (e) {
            var that = this;
        	var rowData=that.Model.getEntryRowData({id:'fentry',row:e.row});
        	
        	var cp = {
        		fusagetype:{
        		    id: rowData.fpurpose.id
        		},
        		pkid:that.Model.pkid,
		        formId:'ydj_supplier',
                callback: function (result) {
                    if (result && result.isSuccess && result.refreshParent) {
                        that.Model.refresh();
                    }
                }
            };
            
            //充值信息对话框
            that.Model.showForm({
                formId: 'coo_inpourdialog',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };

        //扣款
        _child.prototype.debit = function (e) {
            var that = this;
            var rowData = that.Model.getEntryRowData({ id: 'fentry', row: e.row });

            var cp = {
                fusagetype: {
                    id: rowData.fpurpose.id
                },
                pkid: that.Model.pkid,
                formId: 'ydj_supplier',
                callback: function (result) {

                }
            };

            //充值信息对话框
            that.Model.showForm({
                formId: 'coo_chargedialog',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };
        
        //如果已经发过协同则显示协同信息
        _child.prototype.proccooStatusOp = function (coocompany) {
            var that = this;
        	if(coocompany == ' ' || !coocompany){
        		that.Model.setVisible({ id: '.y-cooinfo', value: false });
        		that.Model.setVisible({ id: '[opcode=sendsyn]', value: true });
        	}else{
        		that.Model.setVisible({ id: '.y-cooinfo', value: true });
        		that.Model.setVisible({ id: '[opcode=sendsyn]', value: false });
        	};
        };
        
        //如果单据未保存则隐藏发起协同按钮
        _child.prototype.showSynbtn = function (pkid) {
            var that = this;
            var company = that.Model.getValue({id:'fcoocompany'});
            company = $.trim(company);
        	if(pkid === '' || !pkid || company != ''){
        		that.Model.setVisible({ id: '[opcode=sendsyn]', value: false });
        	}else{
        		that.Model.setVisible({ id: '[opcode=sendsyn]', value: true });
        	};
        };
        
        //如果已经已协同成功则显示协同账户设置
        _child.prototype.showAccount = function (coostate) {
            var that = this;
        	//if(coostate === '已协同'){
        	//	that.Model.setVisible({ id: '.y-synaccount', value: true });
        	//}else{
        	//	that.Model.setVisible({ id: '.y-synaccount', value: false });
        	//};
        };
		
		
		
        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            var listId = that.Model.getSelectRows({});
            if (!args.opcode) return;
            switch (args.opcode) {
                case 'sendsyn':
                    args.result = true;
                    //点击配置按钮显示企业查询弹窗
                    if(!that.Model.pkid){
                    	yiDialog.mt({msg:'请先保存单据再执行配置操作！', skinseq: 2});
                    }else{
                    	that.showCooDialog();
                    }
                    break;
                case 'showcompanyinfo':
                    args.result = true;
                    //点击详情查看企业详情
                    that.showCompanyInfo();
                    break;
                case 'bulksyn':
                    args.result = true;
                    //点击批量协同
                    if(listId.length === 0){
                    	yiDialog.mt({msg:'请选择供应商再执行批量协同邀请操作！', skinseq: 2});
                    }else{
                    	that.bulkSyn();
                    }
                    break;
            }
        };
        
        //显示批量协同窗口
        _child.prototype.bulkSyn = function (e) {
            var that = this;
            var sendType = that.formContext.formId;
            var selectData = [];
            var listId = that.Model.getSelectRows({});
            for(var i=0,j=listId.length;i<j;i++){
            	selectData.push(listId[i].pkValue);
            }
            var cp = {
            	sendType,
            	selectData
            };
            //企业信息数据
            var companyData;
			cp = $.extend(true, cp, companyData);
			//弹出企业详情
            that.Model.showForm({
                formId: 'coo_bulksyn',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };
        
        //显示对应的企业详情对话框
        _child.prototype.showCompanyInfo = function (e) {
            var that = this;
            var companyId = that.Model.uiData.fcoocompanyid;
            var url = '/bill/coo_company?operationno=getCompanyDetail',
            param = {
                simpledata: {
				 	id: companyId
                }
            };
            //请求企业数据
            yiAjax.p(url, param, function (r) {
	            var cp = {
                	
	            };
	            //企业信息数据
	            var companyData = r.operationResult.srvData;
				cp = $.extend(true, cp, companyData);
				//弹出企业详情
	            that.Model.showForm({
	                formId: 'ydj_companyinfo',
	                param: { openStyle: Consts.openStyle.modal },
	                cp: cp
	            });
            });
        };
        
        //显示对话框
        _child.prototype.showCooDialog = function (e) {
            var that = this;
            var basData = that.Model.uiData;
            var formIddata = that.formContext.formId,
                phone = that.Model.uiData.fphone;
            var cp = {
                formIddata: formIddata,
                phone: phone,
                callback: function (result) {
                    if (result && result.isSuccess) {
                        //刷新当前页面
                        that.Model.refresh();
                    }
                }
            };
			cp = $.extend(true, cp, basData);
            //弹出对话框
            that.Model.showForm({
                formId: 'ydj_companyquery',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };
              return _child;
    })(BillPlugIn);
    window.ydj_supplier = window.ydj_supplier || ydj_supplier;
})();