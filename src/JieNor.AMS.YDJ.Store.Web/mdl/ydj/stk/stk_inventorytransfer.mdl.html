<!--
库存调拨单

-->

<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_inventorytransfer" basemodel="base_stkbilltmpl" el="1" cn="库存调拨单" ubl="1">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_invtransfer" pn="fbillhead" cn="库存调拨单">

        <!--修改基类字段名-->
        <input group="基本信息" el="117" ek="FBillHead" id="ftransfertype" fn="ftransfertype" pn="ftransfertype" visible="-1" cn="调拨类型"
               lock="-1" copy="1" lix="1000" notrace="true" ts="" refid="bd_enum" cg="库存调拨单调拨类型" dfld="fenumitem" defval="'invtransfer_biztype_01'" />
        <input group="基本信息" el="152" ek="FBillHead" id="ftransferdirection" fn="ftransferdirection" pn="ftransferdirection" visible="-1" cn="调拨方向"
               lock="-1" copy="1" lix="1000" notrace="true" ts="" vals="'0':'正常','1':'退回'" defval="'0'" />
        <input group="基本信息" el="112" id="fdate" cn="调拨日期" visible="-1" lix="8" />
        <input lix="1" group="基本信息" el="112" id="fplandate" fn="fplandate" cn="计划调拨日期" defval="@currentshortdate" visible="-1" uaul="true" />
        <input group="基本信息" el="152" ek="fbillhead" visible="-1" id="ftype" fn="ftype" pn="ftype" cn="业务类型"
               vals="'1':'仓库调拨','2':'形态转换','3':'货主调整','4':'仓位调拨'" defval="'1'" lix="13" width="90" />
        <input id="fdescription" el="100" visible="1150" canchange="true" lix="200" len="1000" lock="0" uaul="true" notrace="false" />
        <!--调拨发货方信息-->
        <input group="基本信息" el="106" id="fstockstaffid" cn="发货人" visible="-1" lix="18" must="1" />
        <input group="基本信息" el="106" ek="fbillhead" id="fstockdeptid" fn="fstockdeptid" pn="fstockdeptid" cn="发货部门" visible="-1" refid="ydj_dept" lix="17" must="1" />

        <!--调拨接收方信息-->
        <input group="基本信息" el="106" ek="fbillhead" id="fstockstaffidto" fn="fstockstaffidto" pn="fstockstaffidto" visible="-1" cn="收货人"
               lock="0" copy="1" lix="65" notrace="true" ts="" refid="ydj_staff" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="106" ek="fbillhead" id="fstockdeptidto" fn="fstockdeptidto" pn="fstockdeptidto" visible="-1" cn="收货部门"
               copy="1" lix="63" notrace="true" ts="" refid="ydj_dept" filter="" reflvt="0" dfld="" />

        <div el="149" ek="fbillhead" id="forgtypeidto" fn="forgtypeidto" pn="forgtypeidto" lix="190" dataviewname="v_bd_orgdata" cn="收货单位类型" width="100" visible="-1" lock="0">
            <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
            <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
        </div>
        <input el="150" ek="fbillhead" id="forgidto" fn="forgidto" pn="forgidto" lix="70" ctlfk="forgtypeidto" cn="收货单位" width="100" visible="-1" lock="0" />
        <input group="基本信息" el="100" ek="FBillHead" id="forgresponsorto" fn="forgresponsorto" pn="forgresponsorto" visible="-1" cn="收货单位联系人"
               lock="0" copy="1" lix="67" notrace="true" ts="" />

        <input group="基本信息" el="100" ek="fbillhead" id="fdeliveryman" fn="fdeliveryman" pn="fdeliveryman" cn="配送员" visible="1150" uaul="true" />
        <input group="基本信息" el="116" ek="fbillhead" id="fisstockout" fn="fisstockout" pn="fisstockout" visible="1150" cn="已分步式调出" copy="0" lix="0" notrace="true" ts="" defval="false" canchange="true" lock="-1" />
        <input group="基本信息" el="116" ek="fbillhead" id="fispdatask" fn="fispdatask" pn="fispdatask" visible="1150" cn="是否PDA作业" lock="-1" copy="0" lix="70" notrace="true" ts="" />

        <input group="基本信息" el="116" ek="fbillhead" id="fiscallout" fn="fiscallout" pn="fiscallout" visible="0" cn="是否做过调出" copy="0" lix="0" notrace="true" ts="" defval="false" canchange="true" lock="-1" />

        <input group="基本信息" el="106" ek="fbillhead" id="fstorehouseid_h" fn="fstorehouseid" pn="fstorehouseid" visible="-1" cn="调出仓库" lock="0" copy="1" lix="135" notrace="true" ts="" refid="ydj_storehouse" filter="fname!='直发仓库'" reflvt="0" dfld="fmulstore" notrace="false" />
        <input group="基本信息" el="107" ek="fbillhead" visible="0" id="fmulstore" fn="fmulstore" pn="fmulstore" cn="调出仓库门店" lock="-1" ts="" ctlfk="fstorehouseid_h" dispfk="fmulstore" lix="500" />
        <input group="基本信息" el="106" ek="fbillhead" id="fstorehouseidto_h" fn="fstorehouseidto" pn="fstorehouseidto" visible="-1" cn="调入仓库" lock="0" copy="1" lix="135" notrace="true" ts="" refid="ydj_storehouse" filter="fname!='直发仓库'" reflvt="0" dfld="fmulstore" notrace="false" />
        <input group="基本信息" el="107" ek="fbillhead" visible="0" id="fmulstoreto" fn="fmulstoreto" pn="fmulstoreto" cn="调入仓库门店" lock="-1" ts="" ctlfk="fstorehouseidto_h" dispfk="fmulstore" lix="500" />

        <input group="基本信息" el="116" ek="fbillhead" id="fcreateoutscantask" fn="fcreateoutscantask" pn="fcreateoutscantask" visible="1150" cn="已生成PDA调出扫描任务" lock="-1" copy="0" lix="70" notrace="true" ts="" />
        <input group="基本信息" el="116" ek="fbillhead" id="fcreateinscantask" fn="fcreateinscantask" pn="fcreateinscantask" visible="1150" cn="已生成PDA调入扫描任务" lock="-1" copy="0" lix="70" notrace="true" ts="" />
        <input lix="10" el="106" ek="fbillhead" visible="1150" id="foutagentid" fn="foutagentid" pn="foutagentid" refid="bas_agent" cn="调出经销商" notrace="false" lock="-1" copy="0" />
        <input lix="10" el="106" ek="fbillhead" visible="1150" id="finagentid" fn="finagentid" pn="finagentid" refid="bas_agent" cn="调入经销商" notrace="false" copy="0" />
        <input group="基本信息" el="140" ek="fbillhead" visible="0" id="fintransferbilltype" fn="fintransferbilltype" pn="fintransferbilltype" copy="0" lix="60" cn="源单类型" />
        <input group="基本信息" el="141" ek="fbillhead" visible="1150" id="fintransferbillno" fn="fintransferbillno" pn="fintransferbillno" copy="0" lix="61" cn="调入方单据编号" lock="-1" ctlfk="fintransferbilltype" />
        <input group="基本信息" el="165" ek="fbillhead" visible="0" id="fintransferbillid" fn="fintransferbillid" pn="fintransferbillid" copy="0" lix="62" cn="源单ID" desc="调入经销商单据创建时反写" />
        <input group="基本信息" el="113" ek="fbillhead" visible="0" id="fhqderdate" fn="fhqderdate" pn="fhqderdate" cn="提交总部时间" copy="0" lock="-1" />
        <input group="基本信息" el="116" ek="fbillhead" visible="0" id="fhqderstatus" fn="fhqderstatus" pn="fhqderstatus" cn="是否提交总部" copy="0" lock="-1" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstore" fn="fstore" pn="fstore" cn="调出门店名称" notrace="false" refid="bas_store" canchange="true" lix="25" defls="true" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstoreto" fn="fstoreto" pn="fstoreto" cn="调入门店名称" notrace="false" refid="bas_store" canchange="true" lix="25" defls="true" />

    </div>

    <!--调拨明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_stk_invtransferentry" pn="fentity" cn="调拨明细" kfks="fmaterialid">
        <tr>
            <!--修改基类字段属性-->

            <th el="106" ek="fentity" id="fmaterialid" lock="0" must="1"></th>

            <th el="132" ek="fentity" id="fattrinfo" cn="调出辅助属性" lock="0" must="0" lix="100"></th>

            <th el="109" ek="fentity" id="funitid" lock="-1" must="1"></th>
            <th el="109" ek="fentity" id="fbizunitid" cn="调拨单位" lix="103"></th>

            <th el="100" ek="fentity" len="2000" id="fcustomdesc" fn="fcustomdesc" cn="调出定制说明" lix="1000" width="160" visible="1150" must="0"></th>
            <th el="100" ek="fentity" len="2000" id="fcallupcustomdescto" fn="fcallupcustomdescto" cn="调入定制说明" lix="1010" width="160" visible="1150"></th>

            <th el="103" ek="fentity" id="fplanqty" fn="fplanqty" cn="基本单位库存数量" ctlfk="funitid" lix=104 visible="1150" lock="-1" width="120" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizplanqty" fn="fbizplanqty" cn="库存数量" ctlfk="fbizunitid" basqtyfk="fplanqty" lix=104 visible="1150" lock="-1" width="80" format="0,000.00"></th>

            <th el="103" ek="fentity" id="fqty" ts="" cn="基本单位调拨数量" visible="1150" must="1" width="120" lock="-1"></th>
            <th el="103" ek="fentity" id="fbizqty" fn="fbizqty" cn="调拨数量" ctlfk="fbizunitid" basqtyfk="fqty" lix="102" visible="1150" width="80" format="0,000.00"></th>

            <th el="106" ek="fentity" id="fstorehouseid" cn="调出仓库" lock="0" must="0" lix="110"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <th el="153" ek="fentity" id="fstorelocationid" cn="调出仓位" lock="0" lix="120"></th>
            <th el="106" ek="fentity" id="fstockstatus" cn="调出状态" lock="0" must="1" lix="115"></th>

            <th el="100" ek="fentity" id="fmtono" cn="调出物流跟踪号" lock="0" visible="1150" width="120" lix="125"></th>
            <th el="100" ek="fentity" id="fmtonoto" fn="fmtonoto" pn="fmtonoto" cn="调入物流跟踪号" lix="130" width="120" visible="1150" lock="0"></th>

            <th el="149" ek="fentity" id="fownertype" cn="调出货主类型" lock="0" must="0">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th el="150" ek="fentity" id="fownerid" cn="调出货主" lock="0" must="0"></th>

            <!--调入字段-->
            <th el="132" ek="fentity" id="fattrinfoto" fn="fattrinfoto" cn="调入辅助属性" lix="101"
                ctlfk="fmaterialid" pricefk="" width="160" visible="1150"></th>
            <th el="106" ek="fentity" id="fstorehouseidto" fn="fstorehouseidto" pn="fstorehouseidto" visible="1150" cn="调入仓库"
                lock="0" copy="1" lix="135" notrace="true" ts="" refid="ydj_storehouse" filter=" fname!='直发仓库' " reflvt="0" dfld="" notrace="false"></th>
            <th el="153" ek="fentity" id="fstorelocationidto" fn="fstorelocationidto" pn="fstorelocationidto" visible="-1" canchange="true" cn="调入仓位"
                lock="0" copy="1" lix="140" notrace="true" ts="" ctlfk="fstorehouseidto" luek="fentity" lunbfk="fnumber" lunmfk="fname" notrace="false"></th>
            <th el="106" ek="fentity" id="fstockstatusto" fn="fstockstatusto" pn="fstockstatusto" visible="1150" cn="调入状态"
                lock="0" copy="1" lix="145" notrace="true" ts="" refid="ydj_stockstatus" defVal="'311858936800219137'" filter="" reflvt="0" dfld=""></th>

            <th el="112" ek="fentity" id="fplanbackdate" fn="fplanbackdate" pn="fplanbackdate" visible="1150" cn="预计回货日期"
                lock="0" copy="0" lix="184" notrace="true" ts="" width="100"></th>

            <th el="103" ek="fentity" id="ftransferbackqty" fn="ftransferbackqty" pn="ftransferbackqty" visible="1150" cn="基本单位调拨返回数量"
                lock="-1" copy="0" lix="185" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" width="150"></th>
            <th el="103" ek="fentity" id="fbiztransferbackqty" fn="fbiztransferbackqty" pn="fbiztransferbackqty" visible="1150" cn="调拨返回数量"
                lock="-1" copy="0" lix="185" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="ftransferbackqty" roundType="0" format="0,000.00" width="100"></th>

            <th el="149" ek="fentity" id="fownertypeto" fn="fownertypeto" pn="fownertypeto" lix="205" dataviewname="v_bd_ownerdata" cn="调入货主类型" width="100" visible="1150" lock="0">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th el="150" ek="fentity" id="fowneridto" fn="fowneridto" pn="fowneridto" lix="206" ctlfk="fownertypeto" cn="调入货主" width="100" visible="1150" lock="0"></th>

            <!--其它维度字段锁定-->
            <th el="100" ek="fentity" id="flotno" lock="-1" visible="1086"></th>
            <th el="104" ek="fentity" id="fprice" lock="-1" visible="1086"></th>
            <th el="105" ek="fentity" id="famount" lock="-1" visible="1086"></th>

            <th el="103" ek="fentity" id="fstockoutqty" fn="fstockoutqty" pn="fstockoutqty" visible="1150" cn="调出数量" lock="-1" copy="0" lix="185" notrace="true" ts="" roundType="0" format="0,000.00" width="150" ctlfk="fbizunitid" basqtyfk="fqty"></th>

            <th el="103" ek="fentity" id="fstockoutbizqty" fn="fstockoutbizqty" pn="fstockoutbizqty" visible="1150" cn="基本单位调出数量" lock="-1" copy="0" lix="185" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" width="150" basqtyfk="fqty"></th>

            <th el="103" ek="fentity" id="fstockinqty" fn="fstockinqty" pn="fstockinqty" visible="1150" cn="调入数量" lock="-1" copy="0" lix="185" notrace="true" ts="" roundType="0" format="0,000.00" width="150" ctlfk="fbizunitid" basqtyfk="fqty"></th>

            <th el="103" ek="fentity" id="fstockinbizqty" fn="fstockinbizqty" pn="fstockinbizqty" visible="1150" cn="基本单位调入数量" lock="-1" copy="0" lix="185" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" width="150" basqtyfk="fqty"></th>

            <th el="103" ek="fentity" id="fstockoutpackqty" fn="fstockoutpackqty" pn="fstockoutpackqty" visible="1150" cn="调出包数"
                lock="-1" copy="0" lix="185" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" width="150"></th>

            <th el="103" ek="fentity" id="fstockinpackqty" fn="fstockinpackqty" pn="fstockinpackqty" visible="1150" cn="调入包数"
                lock="-1" copy="0" lix="185" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" width="150"></th>

            <th el="112" ek="fentity" id="fstockoutdate" fn="fstockoutdate" pn="fstockoutdate" cn="分步式调出日期" visible="1150" lix="8" lock="-1" copy="0" />

            <th el="112" ek="fentity" id="fstockindate" fn="fstockindate" pn="fstockindate" cn="分步式调入日期" visible="1150" lix="8" lock="-1" copy="0" />

            <th el="107" ek="fentity" id="fisnofifostock" fn="fisnofifostock" pn="fisnofifostock" visible="0" cn="不参与自动推荐" width="80" align="center" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstorehouseid" dispfk="fisnofifostock" refvt="116"></th>

            <!--单位成本，总成本-->
            <th group="调拨明细" el="105" ek="fentity" id="fcostprice" fn="fcostprice" pn="fcostprice" cn="单位成本(加权平均)" />
            <th group="调拨明细" el="105" ek="fentity" id="fcostamt" fn="fcostamt" pn="fcostamt" cn="总成本(加权平均)" />

            <th el="100" lix="299" ek="fentity" id="fvolumeunit" fn="fvolumeunit" pn="fvolumeunit" visible="1086" cn="体积单位" lock="-1" must="0" uaul="true"></th>
            <th el="102" lix="300" ek="fentity" id="ftotalvolume" fn="ftotalvolume" pn="ftotalvolume" visible="1086" cn="总体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
            <th el="102" lix="301" ek="fentity" id="fsinglevolume" fn="fsinglevolume" pn="fsinglevolume" visible="1086" cn="单位体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" cn="收货人、收货部门和收货单位类型不能同时为空"
                data="{'expr':'(fstockdeptidto!=\'\' and fstockdeptidto!=\' \') or (fstockstaffidto!=\'\' and fstockstaffidto!=\' \') or (forgtypeidto!=\'\' and forgtypeidto!=\' \')','message':'收货人、收货部门和收货单位类型不能同时为空！'}"></li>

            <!--<li el="11" vid="510" cn="形态转换调拨时，调出仓库、仓位、库存状态必须同调入仓库、仓位、库存状态相同！" ek="fentity"
            data="{'expr':'fstorehouseidto==fstorehouseid and fstorelocationidto==fstorelocationid and fstockstatusto==fstockstatus','message':'形态转换调拨时，调出仓库、仓位、库存状态必须同调入仓库、仓位、库存状态相同！'}"
            precon="ftype=='2'"></li>-->

            <li el="11" vid="510" cn="非形态转换调拨时，调出跟踪号与调入跟踪号必须相同！" ek="fentity"
                data="{'expr':'fmtonoto==fmtono','message':'非形态转换调拨时，调出跟踪号与调入跟踪号必须相同！'}"
                precon="ftype!='2'"></li>

            <li id="stocklocationcheckto" el="11" vid="3003" cn="仓库启用仓位时必录！" data="{'fieldKey':'fstorelocationidto'}"></li>
            <li el="11" vid="510" ek="fentity" cn="调出货主字段不能为空!" data="{'expr':'(fownertype=\'\' and fownerid=\'\') or (fownertype!=\'\' and fownerid!=\'\')','message':'调出货主字段不能为空!'}"></li>
            <li el="11" vid="510" ek="fentity" cn="调入货主字段不能为空!" data="{'expr':'(fownertypeto=\'\' and fowneridto=\'\') or (fownertypeto!=\'\' and fowneridto!=\'\')','message':'调入货主字段不能为空!'}"></li>
            <!--<li el="11" vid="3008" cn="允许选配的商品明细的辅助属性不能为空" data="{'productFieldKey':'fmaterialid'}"></li>-->

            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\' and fisstockout=False',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" id="draft" op="draft" opn="暂存">


            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\' and fisstockout=False',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>


        <ul el="10" id="submit" op="submit" opn="提交">
            <li el="11" vid="510" cn="收货人、收货部门和收货单位类型不能同时为空"
                data="{'expr':'(fstockdeptidto!=\'\' and fstockdeptidto!=\' \') or (fstockstaffidto!=\'\' and fstockstaffidto!=\' \') or (forgtypeidto!=\'\' and forgtypeidto!=\' \')','message':'收货人、收货部门和收货单位类型不能同时为空！'}"></li>
        </ul>

        <ul el="10" id="audit" op="audit" opn="审核">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="11" vid="510" cn="收货人、收货部门和收货单位类型不能同时为空"
                data="{'expr':'(fstockdeptidto!=\'\' and fstockdeptidto!=\' \') or (fstockstaffidto!=\'\' and fstockstaffidto!=\' \') or (forgtypeidto!=\'\' and forgtypeidto!=\' \')','message':'收货人、收货部门和收货单位类型不能同时为空！'}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <!--调拨前的源维度库存减少-->
            <li el="17" sid="2000" cn="审核时调出方扣减库存" data="{'updInvServiceId':'from','factor':-1,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fstockoutbizqty',
                'stockQtyFieldKey':'fstockoutqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus=\'c\''}"></li>
            <!--调拨后的维度库存增加-->
            <li el="17" sid="2000" cn="审核时调入方增加库存" data="{'updInvServiceId':'to','factor':1,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{'fattrinfo':'fattrinfoto','fattrinfo_e':'fattrinfoto_e','fstorehouseid':'fstorehouseidto','fstorelocationid':'fstorelocationidto','fstockstatus':'fstockstatusto','fmtono':'fmtonoto','fcustomdesc':'fcallupcustomdescto','fownertype':'fownertypeto','fownerid':'fowneridto'},
                'qtyFieldKey':'fstockinbizqty',
                'stockQtyFieldKey':'fstockinqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写调拨申请单已调拨数量" data="{
                'sourceFormId':'stk_inventorytransferreq',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'ftransferqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'ftransferqty&gt;fqty',
                'excessMessage':'调拨数量不允许超过调拨申请数量！'
                }"></li>

            <li el="17" sid="2002" cn="自动释放预留数量" data="{
                'message':'库存调拨单审核，自动释放预留数量',
                'preCondition':'',
                'releaseWay':6,
                'releaseType':0,
                'activeEntityKey':'fentity',
                'SourceTypeFieldKey':'',
                'SourceNumberFieldKey':'',
                'customerFieldKey':'',
                'deptFieldKey':'',
                'staffFieldKey':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\' and fisstockout=False',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>

            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <!--调拨前的源维度库存还原-->
            <li el="17" sid="2000" cn="反审核时取消调出方的库存更新" data="{'updInvServiceId':'from','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fstockoutbizqty',
                'stockQtyFieldKey':'fstockoutqty',
                'amountFieldKey':'famount',
                'preCondition':'fstatus=\'c\''}"></li>
            <!--调拨后的维度库存还原-->
            <li el="17" sid="2000" cn="反审核时取消调入方的库存更新" data="{'updInvServiceId':'to','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{'fattrinfo':'fattrinfoto','fstorehouseid':'fstorehouseidto','fstorelocationid':'fstorelocationidto','fstockstatus':'fstockstatusto','fmtono':'fmtonoto','fcustomdesc':'fcallupcustomdescto','fownertype':'fownertypeto','fownerid':'fowneridto'},
                'qtyFieldKey':'fstockinbizqty',
                'stockQtyFieldKey':'fstockinqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写调拨申请单已调拨数量" data="{
                'sourceFormId':'stk_inventorytransferreq',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'ftransferqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'ftransferqty&gt;fqty',
                'excessMessage':'调拨数量不允许超过调拨申请数量！'
                }"></li>

            <li el="17" sid="2002" cn="取消释放预留数量" data="{
                'message':'库存调拨单反审核，自动取消释放预留数量',
                'preCondition':'',
                'releaseWay':6,
                'releaseType':1,
                'activeEntityKey':'fentity',
                'SourceTypeFieldKey':'',
                'SourceNumberFieldKey':'',
                'customerFieldKey':'',
                'deptFieldKey':'',
                'staffFieldKey':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="11" id="3010_delete" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <!--调拨前的源维度库存还原-->
            <li el="17" sid="2000" cn="删除时还原原维度库存" data="{'updInvServiceId':'from','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus=\'c\''}"></li>
            <!--调拨后的维度库存还原-->
            <li el="17" sid="2000" cn="删除时还原新维度库存" data="{'updInvServiceId':'to','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{'fattrinfo':'fattrinfoto','fstorehouseid':'fstorehouseidto','fstorelocationid':'fstorelocationidto','fstockstatus':'fstockstatusto','fmtono':'fmtonoto','fcustomdesc':'fcallupcustomdescto','fownertype':'fownertypeto','fownerid':'fowneridto'},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写调拨申请单已调拨数量" data="{
                'sourceFormId':'stk_inventorytransferreq',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'ftransferqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'ftransferqty&gt;fqty',
                'excessMessage':'调拨数量不允许超过调拨申请数量！'
                }"></li>

            <li el="17" sid="2006" cn="删除预留" data="{
                'preCondition':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" ek="fentity" id="producttagprintseting" op="producttagprintseting" opn="标签打印" data="{'parameter':{'ruleParam':{'activeEntityKey':'fentity','fieldMappings':[{'id':'fmaterialid','name':'商品','mapType':0,'srcFieldId':'fmaterialid'},{'id':'fattrinfo','name':'辅助属性','mapType':0,'srcFieldId':'fattrinfo'},{'id':'fqty','name':'打印数量','mapType':0,'srcFieldId':'fqty'}]}}}"></ul>

        <ul el="10" ek="fbillhead" id="allotout" op="allotout" opn="调出" ubl="1">
            <!--调拨前的源维度库存减少-->
            <li el="17" sid="2000" cn="调出时更新调出方的库存" data="{'updInvServiceId':'from','factor':-1,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fstockoutbizqty',
                'stockQtyFieldKey':'fstockoutqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus=\'c\''}"></li>

            <li el="17" sid="2002" cn="自动释放预留数量" data="{
                'message':'库存调拨单调出，自动释放预留数量',
                'preCondition':'',
                'releaseWay':6,
                'releaseType':0,
                'activeEntityKey':'fentity',
                'SourceTypeFieldKey':'',
                'SourceNumberFieldKey':'',
                'customerFieldKey':'',
                'deptFieldKey':'',
                'staffFieldKey':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="cancelallotout" op="cancelallotout" opn="取消调出" ubl="1">
            <!--调拨前的源维度库存还原-->
            <li el="17" sid="2000" cn="取消调出时取消调出方的库存更新" data="{'updInvServiceId':'from','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fstockoutbizqty',
                'stockQtyFieldKey':'fstockoutqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus=\'c\''}"></li>
            <!--调拨后的维度库存还原-->
            <!--<li el="17" sid="2000" cn="取消调出时还原新维度库存" data="{'updInvServiceId':'to','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{'fattrinfo':'fattrinfo','fstorehouseid':'fstorehouseid','fstorelocationid':'fstorelocationid','fstockstatus':'fstockstatus','fmtono':'fmtono','fcustomdesc':'fcallupcustomdesc','fownertype':'fownertype','fownerid':'fownerid'},
                'qtyFieldKey':'fbizqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'preCondition':'fstatus=\'c\''}"></li>-->

            <li el="17" sid="2002" cn="取消释放预留数量" data="{
                'message':'库存调拨单取消调出，自动取消释放预留数量',
                'preCondition':'',
                'releaseWay':6,
                'releaseType':1,
                'activeEntityKey':'fentity',
                'SourceTypeFieldKey':'',
                'SourceNumberFieldKey':'',
                'customerFieldKey':'',
                'deptFieldKey':'',
                'staffFieldKey':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>


        <ul el="10" id="cancel" op="cancel" opn="作废">
            <li el="11" id="3010_cancel" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="2006" cn="删除预留" data="{
                'preCondition':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" id="uncancel" op="uncancel" opn="反作废">
            <li el="11" id="3010_uncancel" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\' and fisstockout=False',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>
    </div>

</body>
</html>

