using JieNor.Framework.AppService.QueryBuilder;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;


namespace JieNor.Framework.AppService.ReportService
{

    /// <summary>
    /// 数据查询服务：多用于报表查询，列表查询等场景
    /// </summary>
    [InjectService]
    public class QueryDataService : IQueryDataService
    {

        QueryObject queryObject = null;
        FilterSchemeObject Filter = null;
        bool IsOffice = false;
        UserContext Ctx = null;
        HtmlForm htmlForm = null;
        List<ColumnObject> lstColumns = new List<ColumnObject>();


        public QueryDataInfo QueryData(UserContext ctx, FilterSchemeObject filter, bool isOffice)
        {
            IsOffice = isOffice;
            htmlForm = HtmlParser.LoadFormMetaFromCache(filter.BillFormId, ctx);
            Ctx = ctx;
            if (htmlForm.ElementType == (int)HtmlElementType.HtmlForm_ReportForm)
            {
                Ctx = ctx.CreateQueryDBContext(htmlForm);
            }
            this.Filter = filter;
            GetColunm();
            SqlBuilderParameter param = GetQueryObject();

            param.AddParameter(filter.DynamicParams.OfType<SqlParam>());

            QueryDataInfo data = new QueryDataInfo();
            data.DatasDesc = new ListDesc();
            if (lstColumns == null || lstColumns.Count == 0)
            {
                return data;
            }

            ListDesc desc;
            var reader = BuildQueryData(out desc, param);
            data = PackQueryData(reader, desc, lstColumns);

            data.DataTitle = htmlForm.Caption;
            data.ColHeads = GetReturnCol(BuildColHeads());

            return data;
        }

        private ColumnHeadObject GetReturnCol(ColumnHeadObject colHead)
        {
            ColumnHeadObject head = new ColumnHeadObject();
            head.Caption = htmlForm.Caption;
            foreach (var item in Filter.ColVisible)
            {
                ColumnObject col = GetVisibleCol(item.Id + ".fname", colHead.SubHeads);
                if (col != null)
                {
                    head.AddSubHead(col);
                    continue;
                }
                col = GetVisibleCol(item.Id + ".fenumitem", colHead.SubHeads);
                if (col != null)
                {
                    head.AddSubHead(col);
                    continue;
                }
                col = GetVisibleCol(item.Id + ".fnumber", colHead.SubHeads);
                if (col != null)
                {
                    head.AddSubHead(col);
                    continue;
                }
                col = GetVisibleCol(item.Id + "_txt", colHead.SubHeads);
                if (col != null)
                {
                    var fld = htmlForm.GetField(item.Id);
                    if (!fld.IsNullOrEmptyOrWhiteSpace()
                        && (fld is HtmlImageField || fld is HtmlMulImageField || fld is HtmlMaterialImageField))
                    {
                        var curCol = GetVisibleCol(item.Id, colHead.SubHeads);
                        head.AddSubHead(curCol);
                    }
                    else
                    {
                        head.AddSubHead(col);
                    }
                    continue;
                }
                col = GetVisibleCol(item.Id, colHead.SubHeads);
                if (col != null)
                {
                    head.AddSubHead(col);
                    continue;
                }
            }
            return head;
        }

        private ColumnObject GetVisibleCol(string key, List<ColumnHeadObject> colHeads)
        {
            foreach (var item in colHeads)
            {
                if ((item.SubHeads == null || item.SubHeads.Count == 0)
                    && item.ColInfo != null)
                {
                    if (item.ColInfo.Id.EqualsIgnoreCase(key))
                    {
                        return item.ColInfo;
                    }
                }
                else if (item.SubHeads != null)
                {
                    return GetVisibleCol(key, item.SubHeads);
                }
            }

            return null;
        }



        private ColumnHeadObject BuildColHeads()
        {
            ColumnHeadObject head = new ColumnHeadObject();
            foreach (var item in lstColumns)
            {
                head.AddSubHead(item);
            }

            return head;
        }

        private void GetColunm()
        {
            lstColumns = new List<ColumnObject>();
            //主键
            var idCol = new ColumnObject()
            {
                Id = htmlForm.BillPKFldName,
                FieldId = htmlForm.BillPKFldName,
                Caption = $"{htmlForm.Caption}内码",
                ElementType = HtmlElementType.HtmlField_TextField,
                ListTabIndex = 0,
                EntityKey = "fbillhead",
                Visible = false,
                VisibleScene = HtmlElementVisibleScene.List,
                DBFieldName = htmlForm.HeadEntity.Id + "_Id",
            };
            lstColumns.Add(idCol);

            foreach (var item in Filter.ColVisible)
            {
                if (item.Visible)
                {
                    var fld = htmlForm.GetField(item.Id);
                    if (fld != null)
                    {
                        lstColumns.AddRange(fld.ToListColumn(this.Ctx));
                    }
                }
            }
        }

        private SqlBuilderParameter GetQueryObject()
        {
            SqlBuilderParameter para = new SqlBuilderParameter(this.Ctx, this.Filter.BillFormId);
            para.PageCount = this.Filter.PageCount;
            para.PageIndex = this.Filter.PageIndex;
            para.OrderByString = this.Filter.OrderByString;

            //如果存在子明细字段 但又不存在 明细字段时，则自动添加一个明细字段
            HtmlField subEntryField = null;
            foreach (var item in lstColumns)
            {
                if (subEntryField != null) break;
                var field = this.htmlForm.GetField(item.Id);
                if (field?.Entity is HtmlSubEntryEntity)
                {
                    subEntryField = field;
                }
            }
            if (subEntryField != null)
            {
                var entity = (subEntryField.Entity as HtmlSubEntryEntity)?.ParentEntity;
                HtmlField entryField = null;
                foreach (var item in lstColumns)
                {
                    if (entryField != null) break;
                    var field = this.htmlForm.GetField(item.Id);
                    if (field != null && field.EntityKey.EqualsIgnoreCase(entity?.Id))
                    {
                        entryField = field;
                    }
                }
                if (entryField == null)
                {
                    var entryFields = this.htmlForm.GetEntryFieldList(entity?.Id);
                    var entityField = entryFields?.FirstOrDefault(o => o is HtmlTextField || o is HtmlDecimalField);
                    entityField = entityField ?? entryFields?.First();
                    if (entityField != null)
                    {
                        para.SelectedFieldKeys.Add(entityField.Id);
                    }
                }
            }

            foreach (var item in lstColumns)
            {
                para.SelectedFieldKeys.Add(item.Id);
            }

            para.SetFilter(Filter.FilterData);
            foreach (var item in Filter.CustomerFilter)
            {
                para.SetFilter(item.Key, item.Value.ToString());
            }

            if (!Filter.FilterString.IsNullOrEmptyOrWhiteSpace())
            {
                para.FilterString = " ( {0} ) ".Fmt(Filter.FilterString);
            }

            //设置列表数据隔离条件
            var listQueryBuilder = this.Ctx.Container.GetService<IListSqlBuilder>();
            var listAccessCondition = listQueryBuilder.GetListAccessControlFilter(this.Ctx, para.HtmlForm?.Id ?? para.FormId);
            para.SetFilter(listAccessCondition ?? new List<FilterRowObject>());

            queryObject = QueryService.BuilQueryObject(para);
            foreach (var item in lstColumns)
            {
                var fld = queryObject.SelectFlds.FirstOrDefault(f => f.Id.EqualsIgnoreCase(item.Id) || f.RefFldFullKey.EqualsIgnoreCase(item.Id));
                if (fld != null)
                {
                    item.DBFieldName = fld.ReturnFldName;
                }
                else
                {
                    item.DBFieldName = item.Id;
                }
            }

            return para;
        }


        /// <summary>
        /// 构建查询数据 
        /// </summary>
        private DynamicObjectCollection BuildQueryData(out ListDesc desc, SqlBuilderParameter param)
        {
            desc = new ListDesc();
            var dbSvc = Ctx.Container.GetService<IDBService>();

            using (var reader = dbSvc.ExecuteReader(Ctx, queryObject.BillCountSql, param.DynamicParams))
            {
                while (reader.Read())
                {
                    desc.Bills = Convert.ToInt64(reader[0]);
                    break;
                }
            }

            using (var reader = dbSvc.ExecuteReader(this.Ctx, queryObject.AllCountSql, param.DynamicParams))
            {
                while (reader.Read())
                {
                    desc.Rows = Convert.ToInt64(reader[0]);
                    break;
                }
            }

            using (var reader = dbSvc.ExecuteReader(this.Ctx, queryObject.CurrentCountSql, param.DynamicParams))
            {
                while (reader.Read())
                {
                    desc.CurrentRows = Convert.ToInt64(reader[0]);
                    break;
                }
            }

            if (!string.IsNullOrWhiteSpace(queryObject.SqlSumExpr))
            {
                using (var reader = dbSvc.ExecuteReader(this.Ctx, queryObject.SqlSumExpr, param.DynamicParams))
                {
                    while (reader.Read())
                    {
                        desc.SumAmount = Convert.ToDecimal(reader[0]).ToString("###,###.##");
                        break;
                    }
                }
            }

            var data = dbSvc.ExecuteDynamicObject(this.Ctx, queryObject.Sql, param.DynamicParams);

            return data;
        }


        private QueryDataInfo PackQueryData(DynamicObjectCollection reader, ListDesc desc, List<ColumnObject> cols)
        {
            QueryDataInfo datas = new QueryDataInfo();
            datas.DatasDesc = desc;

            if (cols != null && cols.Count > 0)
            {
                if (IsOffice)
                {
                    datas.OfficeDatas = PackExcelData(reader, cols);
                }
                else
                {
                    datas.Datas = PackData(reader, cols);
                }
            }
            datas.DatasDesc = desc;
            datas.DataTitle = Filter.DataTitle;
            datas.FilterTitle = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            datas.FilterTitle.Add("filter", Filter.FilterTitle);
            return datas;
        }






        private Dictionary<string, List<object>> PackExcelData(DynamicObjectCollection datas, List<ColumnObject> cols)
        {
            Dictionary<string, List<object>> lstListData = new Dictionary<string, List<object>>(StringComparer.OrdinalIgnoreCase);
            for (int colIndex = 0; colIndex < cols.Count; colIndex++)
            {
                if (!lstListData.ContainsKey(cols[colIndex].Id))
                {
                    lstListData.Add(cols[colIndex].Id, new List<object>());
                }
            }
            foreach (var item in datas)
            {
                for (int colIndex = 0; colIndex < cols.Count; colIndex++)
                {
                    var key = cols[colIndex].Id;
                    if (!item.DynamicObjectType.Properties.ContainsKey(cols[colIndex].DBFieldName))
                    {
                        lstListData[key].Add("");
                        continue;
                    }
                    var value = item[cols[colIndex].DBFieldName];
                    if (cols[colIndex].ColType == QueryColTypeEnum.Text)
                    {
                        lstListData[key].Add(string.Format("{0}", value));
                    }
                    else
                    {
                        lstListData[key].Add(value);
                    }
                }
            }

            return lstListData;
        }


        private List<Dictionary<string, object>> PackData(DynamicObjectCollection datas, List<ColumnObject> cols)
        {
            List<Dictionary<string, object>> lstListData = new List<Dictionary<string, object>>();
            foreach (var item in datas)
            {
                Dictionary<string, object> dctRowObj = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                for (int colIndex = 0; colIndex < cols.Count; colIndex++)
                {
                    if (!item.DynamicObjectType.Properties.ContainsKey(cols[colIndex].DBFieldName))
                    {
                        dctRowObj[cols[colIndex].DBFieldName] = "";
                        continue;
                    }
                    dctRowObj[cols[colIndex].DBFieldName] = item[cols[colIndex].DBFieldName];
                }
                lstListData.Add(dctRowObj);
            }

            return lstListData;
        }







    }


}
